<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a client
requireRole(['client']);

$client_id = $_SESSION['client_id'];

// Get client details
$db->query("SELECT * FROM clients WHERE id = :id");
$db->bind(':id', $client_id);
$client = $db->single();

// Get client statistics
$stats = getClientStats($client_id);

// Get recent orders
$db->query("SELECT o.*, g.name as gas_name, g.code as gas_code 
           FROM orders o 
           JOIN gas_types g ON o.gas_type_id = g.id 
           WHERE o.client_id = :client_id 
           ORDER BY o.created_at DESC LIMIT 5");
$db->bind(':client_id', $client_id);
$recent_orders = $db->resultset();

// Get cylinders with client
$db->query("SELECT c.*, g.name as gas_name, g.code as gas_code 
           FROM cylinders c 
           JOIN gas_types g ON c.gas_type_id = g.id 
           WHERE c.current_client_id = :client_id 
           ORDER BY c.updated_at DESC LIMIT 10");
$db->bind(':client_id', $client_id);
$client_cylinders = $db->resultset();

// Get recent invoices
$db->query("SELECT * FROM invoices 
           WHERE client_id = :client_id 
           ORDER BY created_at DESC LIMIT 5");
$db->bind(':client_id', $client_id);
$recent_invoices = $db->resultset();

// Get pending orders count
$db->query("SELECT COUNT(*) as count FROM orders 
           WHERE client_id = :client_id AND status IN ('pending', 'assigned', 'refilling', 'filled', 'loading')");
$db->bind(':client_id', $client_id);
$pending_orders_count = $db->single()->count;

$page_title = 'Client Dashboard';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-tachometer-alt"></i> Welcome, <?php echo htmlspecialchars($client->name); ?>!
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="scanner.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-qrcode"></i> QR Scanner
                        </a>
                        <a href="orders.php" class="btn btn-sm btn-success">
                            <i class="fas fa-plus"></i> New Order Request
                        </a>
                    </div>
                </div>
            </div>

            <!-- Welcome Message -->
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <h5><i class="fas fa-info-circle"></i> Welcome to Sony Enterprises Client Portal!</h5>
                <p class="mb-0">Track your orders, view cylinder status, download invoices, and manage your account from this dashboard.</p>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Orders</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['total_orders']; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending Orders</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $pending_orders_count; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Cylinders</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['cylinders_with_client']; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-fire-extinguisher fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-left-danger shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Outstanding</div>
                                    <div class="h6 mb-0 font-weight-bold text-gray-800"><?php echo formatCurrency($stats['outstanding_amount']); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Row -->
            <div class="row">
                <!-- Recent Orders -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Orders</h6>
                            <a href="orders.php" class="btn btn-sm btn-primary">View All</a>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recent_orders)): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Order #</th>
                                                <th>Gas Type</th>
                                                <th>Quantity</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_orders as $order): ?>
                                            <tr>
                                                <td><strong><?php echo $order->order_number; ?></strong></td>
                                                <td>
                                                    <span class="badge bg-secondary"><?php echo $order->gas_code; ?></span>
                                                    <?php echo $order->gas_name; ?>
                                                </td>
                                                <td><?php echo $order->quantity; ?></td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo $order->status == 'pending' ? 'warning' : 
                                                            ($order->status == 'dispatched' ? 'success' : 'info'); 
                                                    ?>">
                                                        <?php echo ucfirst($order->status); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo formatDate($order->created_at); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                                    <p>No orders found.</p>
                                    <a href="orders.php" class="btn btn-primary">Place Your First Order</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Cylinders with Client -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">Your Cylinders</h6>
                            <a href="cylinders.php" class="btn btn-sm btn-primary">View All</a>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($client_cylinders)): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Cylinder Code</th>
                                                <th>Gas Type</th>
                                                <th>Size</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($client_cylinders as $cylinder): ?>
                                            <tr>
                                                <td><strong><?php echo $cylinder->cylinder_code; ?></strong></td>
                                                <td>
                                                    <span class="badge bg-secondary"><?php echo $cylinder->gas_code; ?></span>
                                                    <?php echo $cylinder->gas_name; ?>
                                                </td>
                                                <td><?php echo $cylinder->size; ?> kg</td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo $cylinder->status == 'filled' ? 'success' : 
                                                            ($cylinder->status == 'empty' ? 'warning' : 'info'); 
                                                    ?>">
                                                        <?php echo ucfirst($cylinder->status); ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-fire-extinguisher fa-3x mb-3"></i>
                                    <p>No cylinders currently with you.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Invoices -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Invoices</h6>
                            <a href="invoices.php" class="btn btn-sm btn-primary">View All</a>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recent_invoices)): ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Invoice #</th>
                                                <th>Date</th>
                                                <th>Total Amount</th>
                                                <th>Paid Amount</th>
                                                <th>Balance</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_invoices as $invoice): ?>
                                            <tr>
                                                <td><strong><?php echo $invoice->invoice_number; ?></strong></td>
                                                <td><?php echo formatDate($invoice->invoice_date); ?></td>
                                                <td><?php echo formatCurrency($invoice->total_amount); ?></td>
                                                <td><?php echo formatCurrency($invoice->paid_amount); ?></td>
                                                <td><?php echo formatCurrency($invoice->balance_amount); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo $invoice->status == 'paid' ? 'success' : 
                                                            ($invoice->status == 'overdue' ? 'danger' : 'warning'); 
                                                    ?>">
                                                        <?php echo ucfirst($invoice->status); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="invoice_view.php?id=<?php echo $invoice->id; ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                    <a href="invoice_download.php?id=<?php echo $invoice->id; ?>" class="btn btn-sm btn-success">
                                                        <i class="fas fa-download"></i> Download
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-file-invoice fa-3x mb-3"></i>
                                    <p>No invoices found.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3 mb-3">
                                    <a href="orders.php" class="btn btn-outline-primary btn-lg w-100">
                                        <i class="fas fa-plus fa-2x mb-2"></i><br>
                                        Place Order
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="scanner.php" class="btn btn-outline-info btn-lg w-100">
                                        <i class="fas fa-qrcode fa-2x mb-2"></i><br>
                                        Scan QR Code
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="cylinders.php" class="btn btn-outline-success btn-lg w-100">
                                        <i class="fas fa-fire-extinguisher fa-2x mb-2"></i><br>
                                        My Cylinders
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="invoices.php" class="btn btn-outline-warning btn-lg w-100">
                                        <i class="fas fa-file-invoice fa-2x mb-2"></i><br>
                                        My Invoices
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
