<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

$errors = [];
$success = '';

// Get clients and gas types
$db->query("SELECT * FROM clients WHERE is_active = 1 ORDER BY name");
$clients = $db->resultset();

$db->query("SELECT * FROM gas_types WHERE is_active = 1 ORDER BY name");
$gas_types = $db->resultset();

// Pre-select client if provided
$selected_client_id = isset($_GET['client_id']) ? (int)$_GET['client_id'] : 0;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $client_id = (int)$_POST['client_id'];
    $gas_type_id = (int)$_POST['gas_type_id'];
    $quantity = (int)$_POST['quantity'];
    $unit_price = (float)$_POST['unit_price'];
    $delivery_date = $_POST['delivery_date'];
    $delivery_address = sanitize($_POST['delivery_address']);
    $special_instructions = sanitize($_POST['special_instructions']);
    $priority = sanitize($_POST['priority']);
    
    // Validation
    if (empty($client_id)) {
        $errors[] = 'Please select a client.';
    }
    
    if (empty($gas_type_id)) {
        $errors[] = 'Please select a gas type.';
    }
    
    if (empty($quantity) || $quantity <= 0) {
        $errors[] = 'Please enter a valid quantity.';
    }
    
    if (empty($unit_price) || $unit_price <= 0) {
        $errors[] = 'Please enter a valid unit price.';
    }
    
    if (empty($delivery_date)) {
        $errors[] = 'Please select a delivery date.';
    } elseif (strtotime($delivery_date) < strtotime('today')) {
        $errors[] = 'Delivery date cannot be in the past.';
    }
    
    if (empty($delivery_address)) {
        $errors[] = 'Please enter delivery address.';
    }
    
    if (empty($errors)) {
        try {
            // Calculate total amount
            $total_amount = $quantity * $unit_price;
            
            // Generate order number
            $order_number = generateOrderNumber();
            
            // Insert order
            $db->query("INSERT INTO orders (
                order_number, client_id, gas_type_id, quantity, unit_price, total_amount,
                delivery_date, delivery_address, special_instructions, priority, status,
                created_by, created_at
            ) VALUES (
                :order_number, :client_id, :gas_type_id, :quantity, :unit_price, :total_amount,
                :delivery_date, :delivery_address, :special_instructions, :priority, 'pending',
                :created_by, NOW()
            )");
            
            $db->bind(':order_number', $order_number);
            $db->bind(':client_id', $client_id);
            $db->bind(':gas_type_id', $gas_type_id);
            $db->bind(':quantity', $quantity);
            $db->bind(':unit_price', $unit_price);
            $db->bind(':total_amount', $total_amount);
            $db->bind(':delivery_date', $delivery_date);
            $db->bind(':delivery_address', $delivery_address);
            $db->bind(':special_instructions', $special_instructions);
            $db->bind(':priority', $priority);
            $db->bind(':created_by', $_SESSION['user_id']);
            
            $db->execute();
            $order_id = $db->lastInsertId();
            
            // Get client details for notification
            $db->query("SELECT name, phone FROM clients WHERE id = :id");
            $db->bind(':id', $client_id);
            $client = $db->single();
            
            // Send WhatsApp notification to client
            if ($client && $client->phone) {
                $message = "Dear {$client->name},\n\n";
                $message .= "Your order has been placed successfully!\n\n";
                $message .= "Order Number: {$order_number}\n";
                $message .= "Quantity: {$quantity} cylinders\n";
                $message .= "Delivery Date: " . formatDate($delivery_date) . "\n";
                $message .= "Total Amount: " . formatCurrency($total_amount) . "\n\n";
                $message .= "We will keep you updated on the progress.\n\n";
                $message .= "Thank you for choosing Sony Enterprises!";
                
                sendNotification($client->phone, $message, 'order_created');
            }
            
            // Log activity
            logActivity($_SESSION['user_id'], 'order_created', "Created order: $order_number for client: {$client->name}");
            
            setFlashMessage('success', "Order created successfully! Order Number: $order_number");
            header('Location: view.php?id=' . $order_id);
            exit();
            
        } catch (Exception $e) {
            $errors[] = 'Error creating order: ' . $e->getMessage();
        }
    }
}

$page_title = 'Create New Order';
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-plus"></i> Create New Order</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Orders
                    </a>
                </div>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Order Information</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="" id="orderForm">
                                <!-- Client Selection -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="client_id" class="form-label">Client <span class="text-danger">*</span></label>
                                        <select class="form-select" id="client_id" name="client_id" required>
                                            <option value="">Select Client</option>
                                            <?php foreach ($clients as $client): ?>
                                                <option value="<?php echo $client->id; ?>" 
                                                        data-phone="<?php echo $client->phone; ?>"
                                                        data-address="<?php echo htmlspecialchars($client->address); ?>"
                                                        <?php echo ($selected_client_id == $client->id || (isset($_POST['client_id']) && $_POST['client_id'] == $client->id)) ? 'selected' : ''; ?>>
                                                    <?php echo $client->name; ?> (<?php echo $client->client_code; ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="gas_type_id" class="form-label">Gas Type <span class="text-danger">*</span></label>
                                        <select class="form-select" id="gas_type_id" name="gas_type_id" required>
                                            <option value="">Select Gas Type</option>
                                            <?php foreach ($gas_types as $gas_type): ?>
                                                <option value="<?php echo $gas_type->id; ?>" 
                                                        data-price="<?php echo $gas_type->price; ?>"
                                                        <?php echo (isset($_POST['gas_type_id']) && $_POST['gas_type_id'] == $gas_type->id) ? 'selected' : ''; ?>>
                                                    <?php echo $gas_type->name; ?> (<?php echo $gas_type->code; ?>) - <?php echo formatCurrency($gas_type->price); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>

                                <!-- Quantity and Pricing -->
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="quantity" class="form-label">Quantity (Cylinders) <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="quantity" name="quantity" 
                                               value="<?php echo isset($_POST['quantity']) ? $_POST['quantity'] : '1'; ?>" 
                                               min="1" required>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="unit_price" class="form-label">Unit Price (₹) <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="unit_price" name="unit_price" 
                                               value="<?php echo isset($_POST['unit_price']) ? $_POST['unit_price'] : ''; ?>" 
                                               step="0.01" min="0" required>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="total_amount" class="form-label">Total Amount (₹)</label>
                                        <input type="text" class="form-control" id="total_amount" readonly>
                                    </div>
                                </div>

                                <!-- Delivery Information -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="delivery_date" class="form-label">Delivery Date <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" id="delivery_date" name="delivery_date" 
                                               value="<?php echo isset($_POST['delivery_date']) ? $_POST['delivery_date'] : date('Y-m-d', strtotime('+1 day')); ?>" 
                                               min="<?php echo date('Y-m-d'); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="priority" class="form-label">Priority</label>
                                        <select class="form-select" id="priority" name="priority">
                                            <option value="normal" <?php echo (isset($_POST['priority']) && $_POST['priority'] == 'normal') ? 'selected' : 'selected'; ?>>Normal</option>
                                            <option value="high" <?php echo (isset($_POST['priority']) && $_POST['priority'] == 'high') ? 'selected' : ''; ?>>High</option>
                                            <option value="urgent" <?php echo (isset($_POST['priority']) && $_POST['priority'] == 'urgent') ? 'selected' : ''; ?>>Urgent</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Delivery Address -->
                                <div class="mb-3">
                                    <label for="delivery_address" class="form-label">Delivery Address <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="delivery_address" name="delivery_address" rows="3" required><?php echo isset($_POST['delivery_address']) ? htmlspecialchars($_POST['delivery_address']) : ''; ?></textarea>
                                    <div class="form-text">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="useClientAddress()">
                                            Use Client's Address
                                        </button>
                                    </div>
                                </div>

                                <!-- Special Instructions -->
                                <div class="mb-3">
                                    <label for="special_instructions" class="form-label">Special Instructions</label>
                                    <textarea class="form-control" id="special_instructions" name="special_instructions" rows="2"><?php echo isset($_POST['special_instructions']) ? htmlspecialchars($_POST['special_instructions']) : ''; ?></textarea>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="index.php" class="btn btn-secondary me-md-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Create Order
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Order Summary -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Order Summary</h6>
                        </div>
                        <div class="card-body">
                            <div id="order-summary">
                                <p class="text-muted">Select client and gas type to see summary</p>
                            </div>
                        </div>
                    </div>

                    <!-- Client Information -->
                    <div class="card shadow mt-3" id="client-info-card" style="display: none;">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Client Information</h6>
                        </div>
                        <div class="card-body">
                            <div id="client-info">
                                <!-- Client details will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Important Notes -->
                    <div class="card shadow mt-3">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Important Notes</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Order Process</h6>
                                <ol class="mb-0">
                                    <li>Order created with "Pending" status</li>
                                    <li>Assign to refilling staff</li>
                                    <li>Cylinders filled and marked ready</li>
                                    <li>Loading and dispatch</li>
                                    <li>Delivery confirmation</li>
                                </ol>
                            </div>

                            <div class="alert alert-success">
                                <h6><i class="fab fa-whatsapp"></i> WhatsApp Notification</h6>
                                <p class="mb-0">Client will receive order confirmation via WhatsApp automatically.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Update unit price when gas type changes
document.getElementById('gas_type_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        const price = selectedOption.dataset.price;
        document.getElementById('unit_price').value = price;
        updateTotal();
        updateOrderSummary();
    }
});

// Update total when quantity or unit price changes
document.getElementById('quantity').addEventListener('input', updateTotal);
document.getElementById('unit_price').addEventListener('input', updateTotal);

// Update client info when client changes
document.getElementById('client_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        showClientInfo(selectedOption);
        updateOrderSummary();
    } else {
        hideClientInfo();
    }
});

function updateTotal() {
    const quantity = parseFloat(document.getElementById('quantity').value) || 0;
    const unitPrice = parseFloat(document.getElementById('unit_price').value) || 0;
    const total = quantity * unitPrice;
    
    document.getElementById('total_amount').value = total.toFixed(2);
    updateOrderSummary();
}

function updateOrderSummary() {
    const clientSelect = document.getElementById('client_id');
    const gasTypeSelect = document.getElementById('gas_type_id');
    const quantity = document.getElementById('quantity').value;
    const unitPrice = document.getElementById('unit_price').value;
    const total = document.getElementById('total_amount').value;
    
    if (clientSelect.value && gasTypeSelect.value) {
        const clientName = clientSelect.options[clientSelect.selectedIndex].text;
        const gasTypeName = gasTypeSelect.options[gasTypeSelect.selectedIndex].text;
        
        document.getElementById('order-summary').innerHTML = `
            <table class="table table-borderless table-sm">
                <tr>
                    <td><strong>Client:</strong></td>
                    <td>${clientName}</td>
                </tr>
                <tr>
                    <td><strong>Gas Type:</strong></td>
                    <td>${gasTypeName}</td>
                </tr>
                <tr>
                    <td><strong>Quantity:</strong></td>
                    <td>${quantity} cylinders</td>
                </tr>
                <tr>
                    <td><strong>Unit Price:</strong></td>
                    <td>₹${unitPrice}</td>
                </tr>
                <tr class="border-top">
                    <td><strong>Total Amount:</strong></td>
                    <td><strong>₹${total}</strong></td>
                </tr>
            </table>
        `;
    } else {
        document.getElementById('order-summary').innerHTML = '<p class="text-muted">Select client and gas type to see summary</p>';
    }
}

function showClientInfo(clientOption) {
    const phone = clientOption.dataset.phone;
    const address = clientOption.dataset.address;
    
    document.getElementById('client-info').innerHTML = `
        <p><strong>Phone:</strong> ${phone}</p>
        <p><strong>Address:</strong><br>${address || 'Not provided'}</p>
        <a href="https://wa.me/91${phone}" target="_blank" class="btn btn-sm btn-success">
            <i class="fab fa-whatsapp"></i> Contact via WhatsApp
        </a>
    `;
    
    document.getElementById('client-info-card').style.display = 'block';
}

function hideClientInfo() {
    document.getElementById('client-info-card').style.display = 'none';
}

function useClientAddress() {
    const clientSelect = document.getElementById('client_id');
    const selectedOption = clientSelect.options[clientSelect.selectedIndex];
    
    if (selectedOption.value) {
        const address = selectedOption.dataset.address;
        if (address) {
            document.getElementById('delivery_address').value = address;
        } else {
            alert('No address found for selected client');
        }
    } else {
        alert('Please select a client first');
    }
}

// Set minimum delivery date to tomorrow
document.getElementById('delivery_date').min = new Date(Date.now() + 86400000).toISOString().split('T')[0];

// Initialize
updateTotal();
</script>

<?php include '../../includes/footer.php'; ?>
