<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

$cylinder_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$cylinder_id) {
    setFlashMessage('error', 'Invalid cylinder ID.');
    header('Location: index.php');
    exit();
}

// Get cylinder details
$db->query("SELECT c.*, g.name as gas_name, g.code as gas_code 
           FROM cylinders c 
           JOIN gas_types g ON c.gas_type_id = g.id 
           WHERE c.id = :id");
$db->bind(':id', $cylinder_id);
$cylinder = $db->single();

if (!$cylinder) {
    setFlashMessage('error', 'Cylinder not found.');
    header('Location: index.php');
    exit();
}

// Generate QR code if not exists
if (empty($cylinder->qr_code)) {
    $qr_data = [
        'cylinder_code' => $cylinder->cylinder_code,
        'gas_type_id' => $cylinder->gas_type_id,
        'size' => $cylinder->size,
        'gas_name' => $cylinder->gas_name
    ];
    $qr_code = generateQRData('cylinder', $cylinder_id, $qr_data);
    
    // Update cylinder with QR code
    $db->query("UPDATE cylinders SET qr_code = :qr_code WHERE id = :id");
    $db->bind(':qr_code', $qr_code);
    $db->bind(':id', $cylinder_id);
    $db->execute();
    
    $cylinder->qr_code = $qr_code;
}

$page_title = 'QR Code - ' . $cylinder->cylinder_code;
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-qrcode"></i> QR Code Generator</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-primary" onclick="printQR()">
                            <i class="fas fa-print"></i> Print QR Label
                        </button>
                        <button type="button" class="btn btn-sm btn-success" onclick="downloadQR()">
                            <i class="fas fa-download"></i> Download QR
                        </button>
                    </div>
                    <a href="view.php?id=<?php echo $cylinder->id; ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Cylinder
                    </a>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header text-center">
                            <h5 class="mb-0">QR Code for Cylinder: <?php echo $cylinder->cylinder_code; ?></h5>
                        </div>
                        <div class="card-body text-center" id="qrContainer">
                            <!-- QR Code will be generated here -->
                            <div id="qrcode" class="mb-4"></div>
                            
                            <!-- Cylinder Information -->
                            <div class="cylinder-info">
                                <h6 class="text-primary">Cylinder Information</h6>
                                <table class="table table-borderless table-sm mx-auto" style="max-width: 400px;">
                                    <tr>
                                        <td><strong>Code:</strong></td>
                                        <td><?php echo $cylinder->cylinder_code; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Gas Type:</strong></td>
                                        <td><?php echo $cylinder->gas_name; ?> (<?php echo $cylinder->gas_code; ?>)</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Size:</strong></td>
                                        <td><?php echo $cylinder->size; ?> kg</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="badge bg-<?php 
                                                echo $cylinder->status == 'available' ? 'success' : 
                                                    ($cylinder->status == 'filled' ? 'info' : 
                                                    ($cylinder->status == 'dispatched' ? 'primary' : 'warning')); 
                                            ?>">
                                                <?php echo ucfirst($cylinder->status); ?>
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            
                            <!-- Company Info -->
                            <div class="company-info mt-4">
                                <?php $company = getCompanySettings(); ?>
                                <h6 class="text-secondary">Sony Enterprises</h6>
                                <p class="text-muted mb-0">
                                    <?php echo htmlspecialchars($company->phone ?? ''); ?><br>
                                    Gas Cylinder Management System
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">QR Code Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> How to Use</h6>
                                <ul class="mb-0">
                                    <li>Print this QR code as a label</li>
                                    <li>Attach to the cylinder</li>
                                    <li>Scan with mobile app for instant access</li>
                                    <li>Track cylinder movement and status</li>
                                </ul>
                            </div>

                            <div class="alert alert-success">
                                <h6><i class="fas fa-mobile-alt"></i> Scanning</h6>
                                <p class="mb-0">Staff and clients can scan this QR code to view cylinder details, update status, and track movement history.</p>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-print"></i> Printing Tips</h6>
                                <ul class="mb-0">
                                    <li>Use waterproof labels</li>
                                    <li>Minimum size: 2cm x 2cm</li>
                                    <li>High contrast printing</li>
                                    <li>Protect from scratches</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- QR Data -->
                    <div class="card shadow mt-3">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">QR Data</h6>
                        </div>
                        <div class="card-body">
                            <small class="text-muted">Encoded Data:</small>
                            <div class="bg-light p-2 rounded mt-2">
                                <code style="font-size: 10px; word-break: break-all;">
                                    <?php echo htmlspecialchars($cylinder->qr_code); ?>
                                </code>
                            </div>
                            
                            <div class="mt-3">
                                <button type="button" class="btn btn-sm btn-outline-secondary w-100" onclick="regenerateQR()">
                                    <i class="fas fa-sync"></i> Regenerate QR Code
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Include QR Code Library -->
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

<script>
// Generate QR Code
document.addEventListener('DOMContentLoaded', function() {
    const qrData = '<?php echo $cylinder->qr_code; ?>';
    
    QRCode.toCanvas(document.getElementById('qrcode'), qrData, {
        width: 300,
        height: 300,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.M
    }, function (error) {
        if (error) {
            console.error(error);
            document.getElementById('qrcode').innerHTML = '<div class="alert alert-danger">Error generating QR code</div>';
        }
    });
});

function printQR() {
    const printContent = document.getElementById('qrContainer').innerHTML;
    const originalContent = document.body.innerHTML;
    
    document.body.innerHTML = `
        <div style="text-align: center; padding: 20px;">
            <style>
                @media print {
                    body { margin: 0; }
                    .cylinder-info table { font-size: 12px; }
                    .company-info { font-size: 10px; }
                }
            </style>
            ${printContent}
        </div>
    `;
    
    window.print();
    document.body.innerHTML = originalContent;
    location.reload();
}

function downloadQR() {
    const canvas = document.querySelector('#qrcode canvas');
    if (canvas) {
        const link = document.createElement('a');
        link.download = 'cylinder_<?php echo $cylinder->cylinder_code; ?>_qr.png';
        link.href = canvas.toDataURL();
        link.click();
    }
}

function regenerateQR() {
    if (confirm('Are you sure you want to regenerate the QR code? The old QR code will no longer work.')) {
        fetch('regenerate_qr.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'cylinder_id=<?php echo $cylinder->id; ?>'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error regenerating QR code: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error regenerating QR code');
        });
    }
}
</script>

<?php include '../../includes/footer.php'; ?>
