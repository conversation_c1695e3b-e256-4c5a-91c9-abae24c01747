<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

// Handle search and filters
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$client_filter = isset($_GET['client']) ? (int)$_GET['client'] : 0;
$gas_type_filter = isset($_GET['gas_type']) ? (int)$_GET['gas_type'] : 0;
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$records_per_page = 25;

// Build query
$where_conditions = ['1=1'];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(o.order_number LIKE :search OR c.name LIKE :search OR c.client_code LIKE :search)";
    $params[':search'] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "o.status = :status";
    $params[':status'] = $status_filter;
}

if (!empty($client_filter)) {
    $where_conditions[] = "o.client_id = :client_id";
    $params[':client_id'] = $client_filter;
}

if (!empty($gas_type_filter)) {
    $where_conditions[] = "o.gas_type_id = :gas_type_id";
    $params[':gas_type_id'] = $gas_type_filter;
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(o.created_at) >= :date_from";
    $params[':date_from'] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(o.created_at) <= :date_to";
    $params[':date_to'] = $date_to;
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count for pagination
$count_query = "SELECT COUNT(*) as total FROM orders o 
               JOIN clients c ON o.client_id = c.id 
               WHERE $where_clause";
$db->query($count_query);
foreach ($params as $key => $value) {
    $db->bind($key, $value);
}
$total_records = $db->single()->total;

// Calculate pagination
$pagination = paginate($total_records, $records_per_page, $page);

// Get orders
$query = "SELECT o.*, c.name as client_name, c.client_code, c.phone as client_phone,
          g.name as gas_name, g.code as gas_code, u1.full_name as created_by_name,
          u2.full_name as assigned_to_name,
          (SELECT COUNT(*) FROM order_cylinders WHERE order_id = o.id) as cylinders_count,
          (SELECT COUNT(*) FROM order_cylinders WHERE order_id = o.id AND filled_at IS NOT NULL) as filled_count
          FROM orders o 
          JOIN clients c ON o.client_id = c.id 
          JOIN gas_types g ON o.gas_type_id = g.id 
          LEFT JOIN users u1 ON o.created_by = u1.id 
          LEFT JOIN users u2 ON o.assigned_to = u2.id 
          WHERE $where_clause 
          ORDER BY o.created_at DESC 
          LIMIT {$pagination['limit']} OFFSET {$pagination['offset']}";

$db->query($query);
foreach ($params as $key => $value) {
    $db->bind($key, $value);
}
$orders = $db->resultset();

// Get filter options
$db->query("SELECT * FROM clients WHERE is_active = 1 ORDER BY name");
$clients = $db->resultset();

$db->query("SELECT * FROM gas_types WHERE is_active = 1 ORDER BY name");
$gas_types = $db->resultset();

// Get statistics
$db->query("SELECT 
    COUNT(*) as total_orders,
    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
    SUM(CASE WHEN status IN ('assigned', 'refilling') THEN 1 ELSE 0 END) as in_progress_orders,
    SUM(CASE WHEN status = 'dispatched' THEN 1 ELSE 0 END) as dispatched_orders,
    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_orders,
    SUM(total_amount) as total_value
    FROM orders");
$stats = $db->single();

$page_title = 'Order Management';
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-clipboard-list"></i> Order Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="create.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> New Order
                        </a>
                        <a href="pending.php" class="btn btn-sm btn-warning">
                            <i class="fas fa-clock"></i> Pending Orders
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToCSV('ordersTable', 'orders')">
                            <i class="fas fa-download"></i> Export CSV
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Orders</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->total_orders; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->pending_orders; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">In Progress</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->in_progress_orders; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-cog fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Dispatched</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->dispatched_orders; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-truck fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Today</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->today_orders; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Value</div>
                                    <div class="h6 mb-0 font-weight-bold text-gray-800"><?php echo formatCurrency($stats->total_value ?? 0); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Order # or client">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="assigned" <?php echo $status_filter === 'assigned' ? 'selected' : ''; ?>>Assigned</option>
                                <option value="refilling" <?php echo $status_filter === 'refilling' ? 'selected' : ''; ?>>Refilling</option>
                                <option value="filled" <?php echo $status_filter === 'filled' ? 'selected' : ''; ?>>Filled</option>
                                <option value="loading" <?php echo $status_filter === 'loading' ? 'selected' : ''; ?>>Loading</option>
                                <option value="dispatched" <?php echo $status_filter === 'dispatched' ? 'selected' : ''; ?>>Dispatched</option>
                                <option value="delivered" <?php echo $status_filter === 'delivered' ? 'selected' : ''; ?>>Delivered</option>
                                <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="client" class="form-label">Client</label>
                            <select class="form-select" id="client" name="client">
                                <option value="">All Clients</option>
                                <?php foreach ($clients as $client): ?>
                                    <option value="<?php echo $client->id; ?>" <?php echo $client_filter == $client->id ? 'selected' : ''; ?>>
                                        <?php echo $client->name; ?> (<?php echo $client->client_code; ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="gas_type" class="form-label">Gas Type</label>
                            <select class="form-select" id="gas_type" name="gas_type">
                                <option value="">All Gas Types</option>
                                <?php foreach ($gas_types as $gas_type): ?>
                                    <option value="<?php echo $gas_type->id; ?>" <?php echo $gas_type_filter == $gas_type->id ? 'selected' : ''; ?>>
                                        <?php echo $gas_type->name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search"></i> Search
                            </button>
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Orders Table -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        Orders List (<?php echo $total_records; ?> total)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="ordersTable">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Date</th>
                                    <th>Client</th>
                                    <th>Gas Type</th>
                                    <th>Quantity</th>
                                    <th>Amount</th>
                                    <th>Progress</th>
                                    <th>Status</th>
                                    <th>Assigned To</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orders as $order): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo $order->order_number; ?></strong>
                                        <br><small class="text-muted"><?php echo formatDateTime($order->created_at); ?></small>
                                    </td>
                                    <td><?php echo formatDate($order->created_at); ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($order->client_name); ?></strong>
                                        <br><small class="text-muted"><?php echo $order->client_code; ?></small>
                                        <br><a href="<?php echo generateWhatsAppURL($order->client_phone, 'Hello ' . $order->client_name . ', regarding your order ' . $order->order_number); ?>" 
                                               target="_blank" class="btn btn-sm btn-success">
                                            <i class="fab fa-whatsapp"></i>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo $order->gas_code; ?></span>
                                        <br><?php echo $order->gas_name; ?>
                                    </td>
                                    <td><?php echo $order->quantity; ?> cylinders</td>
                                    <td><?php echo formatCurrency($order->total_amount); ?></td>
                                    <td>
                                        <?php if ($order->cylinders_count > 0): ?>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: <?php echo ($order->filled_count / $order->cylinders_count) * 100; ?>%">
                                                    <?php echo $order->filled_count; ?>/<?php echo $order->cylinders_count; ?>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">Not assigned</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $order->status == 'pending' ? 'warning' : 
                                                ($order->status == 'assigned' ? 'info' : 
                                                ($order->status == 'refilling' ? 'primary' : 
                                                ($order->status == 'filled' ? 'success' : 
                                                ($order->status == 'dispatched' ? 'success' : 
                                                ($order->status == 'cancelled' ? 'danger' : 'secondary'))))); 
                                        ?>">
                                            <?php echo ucfirst($order->status); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($order->assigned_to_name ?? '-'); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="view.php?id=<?php echo $order->id; ?>" class="btn btn-sm btn-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if ($order->status == 'pending'): ?>
                                            <a href="assign.php?id=<?php echo $order->id; ?>" class="btn btn-sm btn-warning" title="Assign">
                                                <i class="fas fa-user-plus"></i>
                                            </a>
                                            <?php endif; ?>
                                            <a href="edit.php?id=<?php echo $order->id; ?>" class="btn btn-sm btn-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if (hasRole('super_admin') && $order->status == 'pending'): ?>
                                            <a href="cancel.php?id=<?php echo $order->id; ?>" 
                                               class="btn btn-sm btn-danger" title="Cancel" 
                                               onclick="return confirm('Are you sure you want to cancel this order?')">
                                                <i class="fas fa-times"></i>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <?php if ($pagination['has_previous']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $pagination['current_page'] - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&client=<?php echo $client_filter; ?>&gas_type=<?php echo $gas_type_filter; ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>">Previous</a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = 1; $i <= $pagination['total_pages']; $i++): ?>
                                <li class="page-item <?php echo $i == $pagination['current_page'] ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&client=<?php echo $client_filter; ?>&gas_type=<?php echo $gas_type_filter; ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $pagination['current_page'] + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&client=<?php echo $client_filter; ?>&gas_type=<?php echo $gas_type_filter; ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>">Next</a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>
