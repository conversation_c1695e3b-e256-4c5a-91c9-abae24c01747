<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has appropriate role
requireRole(['super_admin', 'office_admin', 'refilling_staff', 'loading_staff']);

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

$cylinder_id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
$code = isset($_POST['code']) ? sanitize($_POST['code']) : '';

if (!$cylinder_id && empty($code)) {
    echo json_encode(['success' => false, 'message' => 'Missing search parameters']);
    exit();
}

try {
    if ($cylinder_id) {
        // Search by ID
        $db->query("SELECT c.*, g.name as gas_name, g.code as gas_code, l.name as location_name, 
                   cl.name as client_name, cl.client_code, cl.phone as client_phone
                   FROM cylinders c 
                   JOIN gas_types g ON c.gas_type_id = g.id 
                   LEFT JOIN locations l ON c.location_id = l.id 
                   LEFT JOIN clients cl ON c.current_client_id = cl.id 
                   WHERE c.id = :id AND c.is_active = 1");
        $db->bind(':id', $cylinder_id);
    } else {
        // Search by code or barcode
        $db->query("SELECT c.*, g.name as gas_name, g.code as gas_code, l.name as location_name, 
                   cl.name as client_name, cl.client_code, cl.phone as client_phone
                   FROM cylinders c 
                   JOIN gas_types g ON c.gas_type_id = g.id 
                   LEFT JOIN locations l ON c.location_id = l.id 
                   LEFT JOIN clients cl ON c.current_client_id = cl.id 
                   WHERE (c.cylinder_code = :code OR c.barcode = :code) AND c.is_active = 1");
        $db->bind(':code', $code);
    }
    
    $cylinder = $db->single();
    
    if ($cylinder) {
        // Log the scan activity
        logActivity($_SESSION['user_id'], 'cylinder_scanned', "Scanned cylinder: {$cylinder->cylinder_code}");
        
        echo json_encode([
            'success' => true,
            'cylinder' => [
                'id' => $cylinder->id,
                'cylinder_code' => $cylinder->cylinder_code,
                'barcode' => $cylinder->barcode,
                'gas_name' => $cylinder->gas_name,
                'gas_code' => $cylinder->gas_code,
                'size' => $cylinder->size,
                'status' => $cylinder->status,
                'location_name' => $cylinder->location_name,
                'client_name' => $cylinder->client_name,
                'client_code' => $cylinder->client_code,
                'client_phone' => $cylinder->client_phone,
                'weight_empty' => $cylinder->weight_empty,
                'weight_full' => $cylinder->weight_full,
                'pressure_rating' => $cylinder->pressure_rating,
                'manufacture_date' => $cylinder->manufacture_date,
                'last_inspection_date' => $cylinder->last_inspection_date,
                'expiry_date' => $cylinder->expiry_date,
                'damage_notes' => $cylinder->damage_notes
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Cylinder not found with the provided code/barcode'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error searching cylinder: ' . $e->getMessage()
    ]);
}
?>
