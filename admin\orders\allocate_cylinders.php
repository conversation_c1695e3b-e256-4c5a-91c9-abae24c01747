<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

$order_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$order_id) {
    setFlashMessage('error', 'Invalid order ID.');
    header('Location: index.php');
    exit();
}

// Get order details
$db->query("SELECT o.*, c.name as client_name, g.name as gas_name, g.id as gas_type_id
           FROM orders o 
           JOIN clients c ON o.client_id = c.id 
           JOIN gas_types g ON o.gas_type_id = g.id 
           WHERE o.id = :id");
$db->bind(':id', $order_id);
$order = $db->single();

if (!$order) {
    setFlashMessage('error', 'Order not found.');
    header('Location: index.php');
    exit();
}

// Check if order can have cylinders allocated
if (!in_array($order->status, ['pending', 'assigned', 'refilling'])) {
    setFlashMessage('error', 'Cylinders can only be allocated to pending, assigned, or refilling orders.');
    header('Location: view.php?id=' . $order_id);
    exit();
}

$errors = [];
$success = '';

// Get already allocated cylinders count
$db->query("SELECT COUNT(*) as allocated_count FROM order_cylinders WHERE order_id = :order_id");
$db->bind(':order_id', $order_id);
$allocated_count = $db->single()->allocated_count;

$remaining_quantity = $order->quantity - $allocated_count;

// Get available cylinders for this gas type
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$location_filter = isset($_GET['location']) ? (int)$_GET['location'] : 0;

$where_conditions = ["c.gas_type_id = :gas_type_id", "c.status = 'available'", "c.is_active = 1"];
$params = [':gas_type_id' => $order->gas_type_id];

if ($search) {
    $where_conditions[] = "(c.cylinder_code LIKE :search OR c.barcode LIKE :search)";
    $params[':search'] = "%$search%";
}

if ($location_filter) {
    $where_conditions[] = "c.location_id = :location_id";
    $params[':location_id'] = $location_filter;
}

$where_clause = implode(' AND ', $where_conditions);

$db->query("SELECT c.*, l.name as location_name 
           FROM cylinders c 
           LEFT JOIN locations l ON c.location_id = l.id 
           WHERE $where_clause 
           ORDER BY c.cylinder_code");

foreach ($params as $key => $value) {
    $db->bind($key, $value);
}
$available_cylinders = $db->resultset();

// Get locations for filter
$db->query("SELECT * FROM locations WHERE is_active = 1 ORDER BY name");
$locations = $db->resultset();

// Get already allocated cylinders
$db->query("SELECT oc.*, c.cylinder_code, c.barcode, c.size, l.name as location_name
           FROM order_cylinders oc
           JOIN cylinders c ON oc.cylinder_id = c.id
           LEFT JOIN locations l ON c.location_id = l.id
           WHERE oc.order_id = :order_id
           ORDER BY oc.created_at");
$db->bind(':order_id', $order_id);
$allocated_cylinders = $db->resultset();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['allocate_cylinders'])) {
        $selected_cylinders = isset($_POST['cylinder_ids']) ? $_POST['cylinder_ids'] : [];
        
        if (empty($selected_cylinders)) {
            $errors[] = 'Please select at least one cylinder to allocate.';
        } else {
            // Check if we're not exceeding the order quantity
            $total_after_allocation = $allocated_count + count($selected_cylinders);
            if ($total_after_allocation > $order->quantity) {
                $errors[] = "Cannot allocate more cylinders. Order quantity: {$order->quantity}, Already allocated: {$allocated_count}, Trying to add: " . count($selected_cylinders);
            }
        }
        
        if (empty($errors)) {
            try {
                $db->beginTransaction();
                
                foreach ($selected_cylinders as $cylinder_id) {
                    // Verify cylinder is still available
                    $db->query("SELECT * FROM cylinders WHERE id = :id AND status = 'available' AND gas_type_id = :gas_type_id");
                    $db->bind(':id', $cylinder_id);
                    $db->bind(':gas_type_id', $order->gas_type_id);
                    $cylinder = $db->single();
                    
                    if (!$cylinder) {
                        throw new Exception("Cylinder ID $cylinder_id is no longer available.");
                    }
                    
                    // Check if cylinder is not already allocated to this order
                    $db->query("SELECT COUNT(*) as count FROM order_cylinders WHERE order_id = :order_id AND cylinder_id = :cylinder_id");
                    $db->bind(':order_id', $order_id);
                    $db->bind(':cylinder_id', $cylinder_id);
                    if ($db->single()->count > 0) {
                        continue; // Skip if already allocated
                    }
                    
                    // Allocate cylinder to order
                    $db->query("INSERT INTO order_cylinders (order_id, cylinder_id, created_at) 
                               VALUES (:order_id, :cylinder_id, NOW())");
                    $db->bind(':order_id', $order_id);
                    $db->bind(':cylinder_id', $cylinder_id);
                    $db->execute();
                    
                    // Update cylinder status
                    $db->query("UPDATE cylinders SET status = 'allocated', current_client_id = :client_id, updated_at = NOW() 
                               WHERE id = :id");
                    $db->bind(':client_id', $order->client_id);
                    $db->bind(':id', $cylinder_id);
                    $db->execute();
                    
                    // Log cylinder movement
                    $db->query("INSERT INTO cylinder_movement_logs 
                               (cylinder_id, movement_type, client_id, order_id, notes, moved_by, movement_date) 
                               VALUES (:cylinder_id, 'outward', :client_id, :order_id, :notes, :moved_by, NOW())");
                    $db->bind(':cylinder_id', $cylinder_id);
                    $db->bind(':client_id', $order->client_id);
                    $db->bind(':order_id', $order_id);
                    $db->bind(':notes', "Allocated to order {$order->order_number}");
                    $db->bind(':moved_by', $_SESSION['user_id']);
                    $db->execute();
                }
                
                $db->commit();
                
                // Log activity
                logActivity($_SESSION['user_id'], 'cylinders_allocated', 
                           "Allocated " . count($selected_cylinders) . " cylinders to order {$order->order_number}");
                
                setFlashMessage('success', count($selected_cylinders) . ' cylinders successfully allocated to the order!');
                header('Location: view.php?id=' . $order_id);
                exit();
                
            } catch (Exception $e) {
                $db->rollback();
                $errors[] = 'Error allocating cylinders: ' . $e->getMessage();
            }
        }
    }
}

$page_title = 'Allocate Cylinders - ' . $order->order_number;
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-gas-pump"></i> Allocate Cylinders</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="view.php?id=<?php echo $order_id; ?>" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Order
                        </a>
                        <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#scannerModal">
                            <i class="fas fa-qrcode"></i> Scan QR/Barcode
                        </button>
                    </div>
                </div>
            </div>

            <!-- Flash Messages -->
            <?php displayFlashMessages(); ?>

            <!-- Display Errors -->
            <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <!-- Order Summary -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Order Summary</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Order Number:</strong></td>
                                    <td><?php echo $order->order_number; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Client:</strong></td>
                                    <td><?php echo $order->client_name; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Gas Type:</strong></td>
                                    <td><?php echo $order->gas_name; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Total Quantity:</strong></td>
                                    <td><?php echo $order->quantity; ?> cylinders</td>
                                </tr>
                                <tr>
                                    <td><strong>Already Allocated:</strong></td>
                                    <td><?php echo $allocated_count; ?> cylinders</td>
                                </tr>
                                <tr>
                                    <td><strong>Remaining:</strong></td>
                                    <td>
                                        <span class="badge bg-<?php echo $remaining_quantity > 0 ? 'warning' : 'success'; ?> fs-6">
                                            <?php echo $remaining_quantity; ?> cylinders
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Allocation Progress</h6>
                        </div>
                        <div class="card-body">
                            <div class="progress mb-3" style="height: 25px;">
                                <?php $progress = $order->quantity > 0 ? ($allocated_count / $order->quantity) * 100 : 0; ?>
                                <div class="progress-bar bg-<?php echo $progress == 100 ? 'success' : 'info'; ?>" 
                                     role="progressbar" style="width: <?php echo $progress; ?>%">
                                    <?php echo round($progress, 1); ?>%
                                </div>
                            </div>
                            <p class="mb-0">
                                <strong><?php echo $allocated_count; ?></strong> of <strong><?php echo $order->quantity; ?></strong> cylinders allocated
                            </p>
                            <?php if ($remaining_quantity == 0): ?>
                            <div class="alert alert-success mt-3 mb-0">
                                <i class="fas fa-check-circle"></i> All cylinders have been allocated to this order!
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <?php if ($remaining_quantity > 0): ?>
            <!-- Search and Filter -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Find Available Cylinders</h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="" class="row g-3">
                        <input type="hidden" name="id" value="<?php echo $order_id; ?>">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Cylinder code or barcode...">
                        </div>
                        <div class="col-md-4">
                            <label for="location" class="form-label">Location</label>
                            <select class="form-select" id="location" name="location">
                                <option value="">All Locations</option>
                                <?php foreach ($locations as $location): ?>
                                <option value="<?php echo $location->id; ?>" <?php echo $location_filter == $location->id ? 'selected' : ''; ?>>
                                    <?php echo $location->name; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Available Cylinders -->
            <form method="POST" action="">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            Available Cylinders (<?php echo count($available_cylinders); ?>) - Gas Type: <?php echo $order->gas_name; ?>
                        </h6>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                                <i class="fas fa-check-square"></i> Select All
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearSelection()">
                                <i class="fas fa-square"></i> Clear All
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($available_cylinders)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-gas-pump fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No available cylinders found</h5>
                            <p class="text-muted">
                                No cylinders of type "<?php echo $order->gas_name; ?>" are currently available for allocation.
                                <?php if ($search || $location_filter): ?>
                                <br><a href="?id=<?php echo $order_id; ?>">Clear filters</a> to see all available cylinders.
                                <?php endif; ?>
                            </p>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAllCheckbox" onchange="toggleAll(this)">
                                        </th>
                                        <th>Cylinder Code</th>
                                        <th>Barcode</th>
                                        <th>Size</th>
                                        <th>Weight (Empty/Full)</th>
                                        <th>Location</th>
                                        <th>Last Inspection</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($available_cylinders as $cylinder): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="cylinder_ids[]" value="<?php echo $cylinder->id; ?>" 
                                                   class="cylinder-checkbox">
                                        </td>
                                        <td>
                                            <strong><?php echo $cylinder->cylinder_code; ?></strong>
                                            <br><small class="text-muted">ID: <?php echo $cylinder->id; ?></small>
                                        </td>
                                        <td><?php echo $cylinder->barcode ?: '-'; ?></td>
                                        <td><?php echo $cylinder->size; ?> kg</td>
                                        <td>
                                            <?php echo $cylinder->weight_empty; ?> / <?php echo $cylinder->weight_full; ?> kg
                                        </td>
                                        <td><?php echo $cylinder->location_name ?: 'Not specified'; ?></td>
                                        <td>
                                            <?php if ($cylinder->last_inspection_date): ?>
                                                <?php echo formatDate($cylinder->last_inspection_date); ?>
                                                <?php
                                                $days_since_inspection = (time() - strtotime($cylinder->last_inspection_date)) / (60 * 60 * 24);
                                                if ($days_since_inspection > 365): ?>
                                                    <br><small class="text-danger">Overdue</small>
                                                <?php elseif ($days_since_inspection > 300): ?>
                                                    <br><small class="text-warning">Due Soon</small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">Not recorded</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="../cylinders/view.php?id=<?php echo $cylinder->id; ?>" 
                                               class="btn btn-sm btn-outline-info" title="View Details" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <span id="selectedCount">0</span> cylinders selected
                                <small class="text-muted">(Maximum: <?php echo $remaining_quantity; ?>)</small>
                            </div>
                            <button type="submit" name="allocate_cylinders" class="btn btn-primary" id="allocateBtn" disabled>
                                <i class="fas fa-gas-pump"></i> Allocate Selected Cylinders
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
            <?php endif; ?>

            <!-- Already Allocated Cylinders -->
            <?php if (!empty($allocated_cylinders)): ?>
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Already Allocated Cylinders (<?php echo count($allocated_cylinders); ?>)</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Cylinder Code</th>
                                    <th>Barcode</th>
                                    <th>Size</th>
                                    <th>Location</th>
                                    <th>Allocated On</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($allocated_cylinders as $cylinder): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo $cylinder->cylinder_code; ?></strong>
                                        <br><small class="text-muted">ID: <?php echo $cylinder->cylinder_id; ?></small>
                                    </td>
                                    <td><?php echo $cylinder->barcode ?: '-'; ?></td>
                                    <td><?php echo $cylinder->size; ?> kg</td>
                                    <td><?php echo $cylinder->location_name ?: '-'; ?></td>
                                    <td><?php echo formatDateTime($cylinder->created_at); ?></td>
                                    <td>
                                        <?php if ($cylinder->filled_at): ?>
                                            <span class="badge bg-success">Filled</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">Allocated</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="../cylinders/view.php?id=<?php echo $cylinder->cylinder_id; ?>" 
                                               class="btn btn-sm btn-outline-info" title="View Details" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if (!$cylinder->filled_at && in_array($order->status, ['pending', 'assigned'])): ?>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="removeCylinder(<?php echo $cylinder->id; ?>)" title="Remove">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<!-- QR/Barcode Scanner Modal -->
<div class="modal fade" id="scannerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Scan Cylinder QR Code or Barcode</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="scanner-container">
                    <div id="reader" style="width: 100%;"></div>
                    <div id="scanner-status" class="mt-3 text-center">
                        <button type="button" class="btn btn-primary" onclick="startScanner()">
                            <i class="fas fa-camera"></i> Start Scanner
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let maxSelection = <?php echo $remaining_quantity; ?>;
let html5QrcodeScanner = null;

function updateSelectedCount() {
    const selected = document.querySelectorAll('.cylinder-checkbox:checked').length;
    document.getElementById('selectedCount').textContent = selected;
    document.getElementById('allocateBtn').disabled = selected === 0;
    
    // Disable checkboxes if max selection reached
    const checkboxes = document.querySelectorAll('.cylinder-checkbox:not(:checked)');
    checkboxes.forEach(cb => {
        cb.disabled = selected >= maxSelection;
    });
}

function toggleAll(masterCheckbox) {
    const checkboxes = document.querySelectorAll('.cylinder-checkbox');
    const shouldCheck = masterCheckbox.checked && document.querySelectorAll('.cylinder-checkbox:checked').length < maxSelection;
    
    let checkedCount = 0;
    checkboxes.forEach(cb => {
        if (shouldCheck && checkedCount < maxSelection) {
            cb.checked = true;
            checkedCount++;
        } else if (!shouldCheck) {
            cb.checked = false;
        }
    });
    
    updateSelectedCount();
}

function selectAll() {
    const checkboxes = document.querySelectorAll('.cylinder-checkbox');
    let checkedCount = 0;
    
    checkboxes.forEach(cb => {
        if (checkedCount < maxSelection) {
            cb.checked = true;
            checkedCount++;
        }
    });
    
    updateSelectedCount();
}

function clearSelection() {
    document.querySelectorAll('.cylinder-checkbox').forEach(cb => cb.checked = false);
    document.getElementById('selectAllCheckbox').checked = false;
    updateSelectedCount();
}

function removeCylinder(orderCylinderId) {
    if (confirm('Are you sure you want to remove this cylinder from the order?')) {
        window.location.href = 'remove_cylinder.php?id=' + orderCylinderId + '&order_id=<?php echo $order_id; ?>';
    }
}

// Scanner functions
function startScanner() {
    if (html5QrcodeScanner) {
        html5QrcodeScanner.clear();
    }
    
    html5QrcodeScanner = new Html5QrcodeScanner("reader", {
        fps: 10,
        qrbox: { width: 250, height: 250 }
    }, false);
    
    html5QrcodeScanner.render(onScanSuccess, onScanFailure);
    document.getElementById('scanner-status').innerHTML = '<p class="text-success">Scanner active - Point camera at QR code or barcode</p>';
}

function onScanSuccess(decodedText, decodedResult) {
    // Search for cylinder by code or barcode
    const cylinders = document.querySelectorAll('tbody tr');
    let found = false;
    
    cylinders.forEach(row => {
        const code = row.cells[1].textContent.trim();
        const barcode = row.cells[2].textContent.trim();
        
        if (code.includes(decodedText) || barcode.includes(decodedText)) {
            const checkbox = row.querySelector('.cylinder-checkbox');
            if (checkbox && !checkbox.checked && !checkbox.disabled) {
                checkbox.checked = true;
                row.style.backgroundColor = '#d4edda';
                found = true;
                updateSelectedCount();
            }
        }
    });
    
    if (found) {
        document.getElementById('scanner-status').innerHTML = '<p class="text-success">Cylinder found and selected!</p>';
    } else {
        document.getElementById('scanner-status').innerHTML = '<p class="text-warning">Cylinder not found in available list</p>';
    }
    
    // Auto-close scanner after 2 seconds
    setTimeout(() => {
        if (html5QrcodeScanner) {
            html5QrcodeScanner.clear();
            $('#scannerModal').modal('hide');
        }
    }, 2000);
}

function onScanFailure(error) {
    // Handle scan failure silently
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.cylinder-checkbox').forEach(cb => {
        cb.addEventListener('change', updateSelectedCount);
    });
    updateSelectedCount();
});
</script>

<?php include '../../includes/footer.php'; ?>
