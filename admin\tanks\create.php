<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

$errors = [];
$success = '';

// Get gas types and locations
$db->query("SELECT * FROM gas_types WHERE is_active = 1 ORDER BY name");
$gas_types = $db->resultset();

$db->query("SELECT * FROM locations WHERE is_active = 1 ORDER BY name");
$locations = $db->resultset();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $gas_type_id = (int)$_POST['gas_type_id'];
    $location_id = (int)$_POST['location_id'];
    $capacity = (float)$_POST['capacity'];
    $unit = sanitize($_POST['unit']);
    $current_level = !empty($_POST['current_level']) ? (float)$_POST['current_level'] : 0;
    $wastage_threshold = !empty($_POST['wastage_threshold']) ? (float)$_POST['wastage_threshold'] : 20.00;
    $auto_generate_code = isset($_POST['auto_generate_code']) ? 1 : 0;
    $tank_code = $auto_generate_code ? '' : sanitize($_POST['tank_code']);
    
    // Validation
    if (empty($gas_type_id)) {
        $errors[] = 'Please select a gas type.';
    }
    
    if (empty($location_id)) {
        $errors[] = 'Please select a location.';
    }
    
    if (empty($capacity) || $capacity <= 0) {
        $errors[] = 'Please enter a valid tank capacity.';
    }
    
    if (empty($unit)) {
        $errors[] = 'Please select a unit.';
    }
    
    if ($current_level > $capacity) {
        $errors[] = 'Current level cannot be greater than capacity.';
    }
    
    if (!$auto_generate_code && empty($tank_code)) {
        $errors[] = 'Please enter a tank code or enable auto-generation.';
    }
    
    // Generate tank code if auto-generation is enabled
    if ($auto_generate_code && empty($errors)) {
        // Get gas type and location for code generation
        $db->query("SELECT g.code as gas_code, l.name as location_name 
                   FROM gas_types g, locations l 
                   WHERE g.id = :gas_id AND l.id = :location_id");
        $db->bind(':gas_id', $gas_type_id);
        $db->bind(':location_id', $location_id);
        $tank_info = $db->single();
        
        if ($tank_info) {
            // Generate code: GASTYPE-LOCATION-001
            $gas_prefix = strtoupper(substr($tank_info->gas_code, 0, 2));
            $location_prefix = strtoupper(substr($tank_info->location_name, 0, 3));
            
            // Get next number for this combination
            $db->query("SELECT tank_code FROM tanks WHERE tank_code LIKE :pattern ORDER BY id DESC LIMIT 1");
            $db->bind(':pattern', $gas_prefix . $location_prefix . '%');
            $last_tank = $db->single();
            
            if ($last_tank) {
                $last_number = (int) substr($last_tank->tank_code, -3);
                $new_number = $last_number + 1;
            } else {
                $new_number = 1;
            }
            
            $tank_code = $gas_prefix . $location_prefix . str_pad($new_number, 3, '0', STR_PAD_LEFT);
        } else {
            $errors[] = 'Invalid gas type or location selected.';
        }
    }
    
    // Check if tank code already exists
    if (!empty($tank_code) && empty($errors)) {
        $db->query("SELECT id FROM tanks WHERE tank_code = :tank_code");
        $db->bind(':tank_code', $tank_code);
        if ($db->single()) {
            $errors[] = 'Tank code already exists.';
        }
    }
    
    if (empty($errors)) {
        try {
            // Insert tank
            $db->query("INSERT INTO tanks (
                tank_code, gas_type_id, location_id, capacity, current_level, unit, 
                wastage_threshold, created_at
            ) VALUES (
                :tank_code, :gas_type_id, :location_id, :capacity, :current_level, :unit, 
                :wastage_threshold, NOW()
            )");
            
            $db->bind(':tank_code', $tank_code);
            $db->bind(':gas_type_id', $gas_type_id);
            $db->bind(':location_id', $location_id);
            $db->bind(':capacity', $capacity);
            $db->bind(':current_level', $current_level);
            $db->bind(':unit', $unit);
            $db->bind(':wastage_threshold', $wastage_threshold);
            
            $db->execute();
            $tank_id = $db->lastInsertId();
            
            // Log initial level if greater than 0
            if ($current_level > 0) {
                $db->query("INSERT INTO tank_refill_logs (
                    tank_id, quantity_added, previous_level, new_level, 
                    refill_date, notes, created_by, created_at
                ) VALUES (
                    :tank_id, :quantity_added, 0, :new_level, 
                    CURDATE(), 'Initial tank setup', :created_by, NOW()
                )");
                
                $db->bind(':tank_id', $tank_id);
                $db->bind(':quantity_added', $current_level);
                $db->bind(':new_level', $current_level);
                $db->bind(':created_by', $_SESSION['user_id']);
                $db->execute();
            }
            
            // Log activity
            logActivity($_SESSION['user_id'], 'tank_created', "Created tank: $tank_code");
            
            setFlashMessage('success', "Tank created successfully! Tank Code: $tank_code");
            header('Location: view.php?id=' . $tank_id);
            exit();
            
        } catch (Exception $e) {
            $errors[] = 'Error creating tank: ' . $e->getMessage();
        }
    }
}

$page_title = 'Add New Tank';
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-plus"></i> Add New Tank</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Tanks
                    </a>
                </div>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Tank Information</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <!-- Basic Information -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="gas_type_id" class="form-label">Gas Type <span class="text-danger">*</span></label>
                                        <select class="form-select" id="gas_type_id" name="gas_type_id" required>
                                            <option value="">Select Gas Type</option>
                                            <?php foreach ($gas_types as $gas_type): ?>
                                                <option value="<?php echo $gas_type->id; ?>" 
                                                        <?php echo (isset($_POST['gas_type_id']) && $_POST['gas_type_id'] == $gas_type->id) ? 'selected' : ''; ?>>
                                                    <?php echo $gas_type->name; ?> (<?php echo $gas_type->code; ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="location_id" class="form-label">Location <span class="text-danger">*</span></label>
                                        <select class="form-select" id="location_id" name="location_id" required>
                                            <option value="">Select Location</option>
                                            <?php foreach ($locations as $location): ?>
                                                <option value="<?php echo $location->id; ?>" 
                                                        <?php echo (isset($_POST['location_id']) && $_POST['location_id'] == $location->id) ? 'selected' : ''; ?>>
                                                    <?php echo $location->name; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>

                                <!-- Tank Code -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="auto_generate_code" name="auto_generate_code" 
                                                   <?php echo isset($_POST['auto_generate_code']) ? 'checked' : 'checked'; ?>>
                                            <label class="form-check-label" for="auto_generate_code">
                                                Auto-generate Tank Code
                                            </label>
                                        </div>
                                        <label for="tank_code" class="form-label">Tank Code</label>
                                        <input type="text" class="form-control" id="tank_code" name="tank_code" 
                                               value="<?php echo isset($_POST['tank_code']) ? htmlspecialchars($_POST['tank_code']) : ''; ?>"
                                               placeholder="Will be auto-generated">
                                    </div>
                                </div>

                                <!-- Capacity and Unit -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="capacity" class="form-label">Tank Capacity <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="capacity" name="capacity" 
                                               value="<?php echo isset($_POST['capacity']) ? $_POST['capacity'] : ''; ?>" 
                                               step="0.1" min="0" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="unit" class="form-label">Unit <span class="text-danger">*</span></label>
                                        <select class="form-select" id="unit" name="unit" required>
                                            <option value="">Select Unit</option>
                                            <option value="kg" <?php echo (isset($_POST['unit']) && $_POST['unit'] == 'kg') ? 'selected' : 'selected'; ?>>Kilograms (kg)</option>
                                            <option value="liter" <?php echo (isset($_POST['unit']) && $_POST['unit'] == 'liter') ? 'selected' : ''; ?>>Liters (L)</option>
                                            <option value="m3" <?php echo (isset($_POST['unit']) && $_POST['unit'] == 'm3') ? 'selected' : ''; ?>>Cubic Meters (m³)</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Current Level and Threshold -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="current_level" class="form-label">Current Level</label>
                                        <input type="number" class="form-control" id="current_level" name="current_level" 
                                               value="<?php echo isset($_POST['current_level']) ? $_POST['current_level'] : '0'; ?>" 
                                               step="0.1" min="0">
                                        <div class="form-text">Current gas level in the tank</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="wastage_threshold" class="form-label">Low Level Threshold (%)</label>
                                        <input type="number" class="form-control" id="wastage_threshold" name="wastage_threshold" 
                                               value="<?php echo isset($_POST['wastage_threshold']) ? $_POST['wastage_threshold'] : '20'; ?>" 
                                               step="0.1" min="0" max="100">
                                        <div class="form-text">Alert when level falls below this percentage</div>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="index.php" class="btn btn-secondary me-md-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Create Tank
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Tank Preview</h6>
                        </div>
                        <div class="card-body text-center">
                            <div class="tank-animation mx-auto mb-3" id="previewTank" 
                                 style="width: 100px; height: 150px; position: relative; border: 3px solid #333; border-radius: 0 0 50px 50px; background: #f0f0f0; overflow: hidden;">
                                <div class="tank-fill" id="previewFill" style="position: absolute; bottom: 0; width: 100%; height: 0%; 
                                     background: linear-gradient(135deg, #28a745, #4caf50); transition: height 0.5s ease;">
                                </div>
                            </div>
                            <div id="previewLevel">0%</div>
                            <div id="previewInfo" class="text-muted">
                                <small>Enter capacity and current level to see preview</small>
                            </div>
                        </div>
                    </div>

                    <div class="card shadow mt-3">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Important Notes</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Tank Code</h6>
                                <p class="mb-0">Auto-generated codes follow the format: [GAS][LOCATION][NUMBER] (e.g., OXMAI001 for Oxygen tank at Main location)</p>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Low Level Alert</h6>
                                <p class="mb-0">Set an appropriate threshold percentage to receive alerts when tank levels are low and need refilling.</p>
                            </div>

                            <div class="alert alert-success">
                                <h6><i class="fas fa-chart-line"></i> Monitoring</h6>
                                <p class="mb-0">Tank levels are monitored in real-time and displayed with animated gauges on the dashboard.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Toggle tank code input based on auto-generation checkbox
document.getElementById('auto_generate_code').addEventListener('change', function() {
    const tankCodeInput = document.getElementById('tank_code');
    if (this.checked) {
        tankCodeInput.disabled = true;
        tankCodeInput.placeholder = 'Will be auto-generated';
        tankCodeInput.value = '';
    } else {
        tankCodeInput.disabled = false;
        tankCodeInput.placeholder = 'Enter tank code';
    }
});

// Initialize the state
document.getElementById('auto_generate_code').dispatchEvent(new Event('change'));

// Update preview when capacity or current level changes
function updatePreview() {
    const capacity = parseFloat(document.getElementById('capacity').value) || 0;
    const currentLevel = parseFloat(document.getElementById('current_level').value) || 0;
    const unit = document.getElementById('unit').value || 'kg';
    
    if (capacity > 0) {
        const percentage = Math.min((currentLevel / capacity) * 100, 100);
        
        document.getElementById('previewFill').style.height = percentage + '%';
        document.getElementById('previewLevel').textContent = percentage.toFixed(1) + '%';
        document.getElementById('previewInfo').innerHTML = `
            <small>
                ${currentLevel} / ${capacity} ${unit}<br>
                ${percentage < 20 ? '<span class="text-danger">Low Level</span>' : 
                  percentage < 50 ? '<span class="text-warning">Medium Level</span>' : 
                  '<span class="text-success">Good Level</span>'}
            </small>
        `;
        
        // Update fill color based on level
        const fill = document.getElementById('previewFill');
        if (percentage < 20) {
            fill.style.background = 'linear-gradient(135deg, #dc3545, #ff6b6b)';
        } else if (percentage < 50) {
            fill.style.background = 'linear-gradient(135deg, #ffc107, #ffeb3b)';
        } else {
            fill.style.background = 'linear-gradient(135deg, #28a745, #4caf50)';
        }
    } else {
        document.getElementById('previewFill').style.height = '0%';
        document.getElementById('previewLevel').textContent = '0%';
        document.getElementById('previewInfo').innerHTML = '<small>Enter capacity and current level to see preview</small>';
    }
}

// Add event listeners for preview updates
document.getElementById('capacity').addEventListener('input', updatePreview);
document.getElementById('current_level').addEventListener('input', updatePreview);
document.getElementById('unit').addEventListener('change', updatePreview);

// Validate current level doesn't exceed capacity
document.getElementById('current_level').addEventListener('input', function() {
    const capacity = parseFloat(document.getElementById('capacity').value) || 0;
    const currentLevel = parseFloat(this.value) || 0;
    
    if (currentLevel > capacity && capacity > 0) {
        this.setCustomValidity('Current level cannot exceed tank capacity');
    } else {
        this.setCustomValidity('');
    }
});

// Initialize preview
updatePreview();
</script>

<?php include '../../includes/footer.php'; ?>
