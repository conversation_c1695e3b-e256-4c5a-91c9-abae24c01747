<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is staff
requireRole(['refilling_staff', 'loading_staff']);

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Get assigned orders for refilling staff
if ($user_role == 'refilling_staff') {
    $db->query("SELECT o.*, c.name as client_name, c.client_code, g.name as gas_name, g.code as gas_code,
               (SELECT COUNT(*) FROM order_cylinders WHERE order_id = o.id) as total_cylinders,
               (SELECT COUNT(*) FROM order_cylinders WHERE order_id = o.id AND filled_at IS NOT NULL) as filled_cylinders
               FROM orders o 
               JOIN clients c ON o.client_id = c.id 
               JOIN gas_types g ON o.gas_type_id = g.id 
               WHERE o.assigned_to = :user_id AND o.status IN ('assigned', 'refilling') 
               ORDER BY o.priority DESC, o.delivery_date ASC");
    $db->bind(':user_id', $user_id);
    $assigned_orders = $db->resultset();
}

// Get orders ready for loading (for loading staff)
if ($user_role == 'loading_staff') {
    $db->query("SELECT o.*, c.name as client_name, c.client_code, g.name as gas_name, g.code as gas_code,
               (SELECT COUNT(*) FROM order_cylinders WHERE order_id = o.id) as total_cylinders,
               (SELECT COUNT(*) FROM order_cylinders WHERE order_id = o.id AND loaded_at IS NOT NULL) as loaded_cylinders
               FROM orders o 
               JOIN clients c ON o.client_id = c.id 
               JOIN gas_types g ON o.gas_type_id = g.id 
               WHERE o.status IN ('filled', 'loading') 
               ORDER BY o.priority DESC, o.delivery_date ASC");
    $ready_orders = $db->resultset();
}

// Get today's statistics
$db->query("SELECT 
    COUNT(CASE WHEN status = 'assigned' AND assigned_to = :user_id THEN 1 END) as assigned_today,
    COUNT(CASE WHEN status = 'refilling' AND assigned_to = :user_id THEN 1 END) as in_progress,
    COUNT(CASE WHEN status = 'filled' AND assigned_to = :user_id AND DATE(updated_at) = CURDATE() THEN 1 END) as completed_today
    FROM orders");
$db->bind(':user_id', $user_id);
$stats = $db->single();

// Get tank levels for refilling staff
if ($user_role == 'refilling_staff') {
    $db->query("SELECT t.*, g.name as gas_name, g.code as gas_code, l.name as location_name,
               ROUND((t.current_level / t.capacity) * 100, 2) as level_percentage
               FROM tanks t 
               JOIN gas_types g ON t.gas_type_id = g.id 
               JOIN locations l ON t.location_id = l.id 
               WHERE t.is_active = 1 
               ORDER BY level_percentage ASC");
    $tanks = $db->resultset();
}

$page_title = 'Staff Dashboard';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-hard-hat"></i> 
                    <?php echo $user_role == 'refilling_staff' ? 'Refilling' : 'Loading'; ?> Dashboard
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="scanner.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-qrcode"></i> QR Scanner
                        </a>
                        <?php if ($user_role == 'refilling_staff'): ?>
                        <a href="tanks.php" class="btn btn-sm btn-info">
                            <i class="fas fa-gas-pump"></i> Tank Levels
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <?php if ($user_role == 'refilling_staff'): ?>
                <div class="col-md-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Assigned Orders</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->assigned_today; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">In Progress</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->in_progress; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-cog fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Completed Today</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->completed_today; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <!-- Loading Staff Stats -->
                <div class="col-md-6">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Ready for Loading</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo count($ready_orders ?? []); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-truck-loading fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Dispatched Today</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $db->query("SELECT COUNT(*) as count FROM orders WHERE status = 'dispatched' AND DATE(updated_at) = CURDATE()");
                                        echo $db->single()->count;
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-truck fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <div class="row">
                <!-- Assigned Orders / Ready Orders -->
                <div class="col-lg-8 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <?php echo $user_role == 'refilling_staff' ? 'My Assigned Orders' : 'Orders Ready for Loading'; ?>
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php 
                            $orders = $user_role == 'refilling_staff' ? ($assigned_orders ?? []) : ($ready_orders ?? []);
                            if (!empty($orders)): 
                            ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Order #</th>
                                                <th>Client</th>
                                                <th>Gas Type</th>
                                                <th>Progress</th>
                                                <th>Priority</th>
                                                <th>Delivery Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($orders as $order): ?>
                                            <tr>
                                                <td><strong><?php echo $order->order_number; ?></strong></td>
                                                <td>
                                                    <?php echo htmlspecialchars($order->client_name); ?>
                                                    <br><small class="text-muted"><?php echo $order->client_code; ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary"><?php echo $order->gas_code; ?></span>
                                                    <?php echo $order->gas_name; ?>
                                                </td>
                                                <td>
                                                    <?php if ($user_role == 'refilling_staff'): ?>
                                                        <div class="progress" style="height: 20px;">
                                                            <div class="progress-bar" role="progressbar" 
                                                                 style="width: <?php echo $order->total_cylinders > 0 ? ($order->filled_cylinders / $order->total_cylinders) * 100 : 0; ?>%">
                                                                <?php echo $order->filled_cylinders; ?>/<?php echo $order->total_cylinders; ?>
                                                            </div>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="progress" style="height: 20px;">
                                                            <div class="progress-bar bg-success" role="progressbar" 
                                                                 style="width: <?php echo $order->total_cylinders > 0 ? ($order->loaded_cylinders / $order->total_cylinders) * 100 : 0; ?>%">
                                                                <?php echo $order->loaded_cylinders; ?>/<?php echo $order->total_cylinders; ?>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo $order->priority == 'urgent' ? 'danger' : 
                                                            ($order->priority == 'high' ? 'warning' : 'info'); 
                                                    ?>">
                                                        <?php echo ucfirst($order->priority); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo formatDate($order->delivery_date); ?>
                                                    <?php if (strtotime($order->delivery_date) <= strtotime('+1 day')): ?>
                                                        <br><span class="badge bg-warning">Due Soon</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="order_details.php?id=<?php echo $order->id; ?>" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if ($user_role == 'refilling_staff'): ?>
                                                        <a href="refill_order.php?id=<?php echo $order->id; ?>" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-fill-drip"></i>
                                                        </a>
                                                        <?php else: ?>
                                                        <a href="load_order.php?id=<?php echo $order->id; ?>" class="btn btn-sm btn-success">
                                                            <i class="fas fa-truck-loading"></i>
                                                        </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                                    <p><?php echo $user_role == 'refilling_staff' ? 'No orders assigned to you.' : 'No orders ready for loading.'; ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Tank Levels (for refilling staff) or Quick Actions -->
                <div class="col-lg-4 mb-4">
                    <?php if ($user_role == 'refilling_staff'): ?>
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Tank Levels</h6>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($tanks)): ?>
                                <?php foreach (array_slice($tanks, 0, 5) as $tank): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <strong><?php echo $tank->tank_code; ?></strong>
                                        <br><small class="text-muted"><?php echo $tank->gas_name; ?></small>
                                    </div>
                                    <div class="text-end">
                                        <div class="progress" style="width: 100px; height: 20px;">
                                            <div class="progress-bar bg-<?php 
                                                echo $tank->level_percentage < 20 ? 'danger' : 
                                                    ($tank->level_percentage < 50 ? 'warning' : 'success'); 
                                            ?>" 
                                                 role="progressbar" style="width: <?php echo $tank->level_percentage; ?>%">
                                            </div>
                                        </div>
                                        <small><?php echo $tank->level_percentage; ?>%</small>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                                <div class="text-center">
                                    <a href="tanks.php" class="btn btn-sm btn-primary">View All Tanks</a>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">No tank data available.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php else: ?>
                    <!-- Quick Actions for Loading Staff -->
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="scanner.php" class="btn btn-primary">
                                    <i class="fas fa-qrcode"></i> Scan Cylinder QR
                                </a>
                                <a href="dispatch_list.php" class="btn btn-info">
                                    <i class="fas fa-list"></i> Dispatch List
                                </a>
                                <a href="vehicle_loading.php" class="btn btn-success">
                                    <i class="fas fa-truck"></i> Vehicle Loading
                                </a>
                                <a href="delivery_confirmation.php" class="btn btn-warning">
                                    <i class="fas fa-check-circle"></i> Delivery Confirmation
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            // Get recent activity for this user
                            $db->query("SELECT * FROM activity_logs 
                                       WHERE user_id = :user_id 
                                       ORDER BY created_at DESC LIMIT 10");
                            $db->bind(':user_id', $user_id);
                            $activities = $db->resultset();
                            ?>
                            
                            <?php if (!empty($activities)): ?>
                                <div class="timeline">
                                    <?php foreach ($activities as $activity): ?>
                                    <div class="timeline-item">
                                        <div class="timeline-marker"></div>
                                        <div class="timeline-content">
                                            <h6 class="timeline-title"><?php echo ucfirst(str_replace('_', ' ', $activity->action)); ?></h6>
                                            <p class="timeline-text"><?php echo htmlspecialchars($activity->description); ?></p>
                                            <small class="text-muted"><?php echo formatDateTime($activity->created_at); ?></small>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">No recent activity.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #007bff;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #007bff;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: -31px;
    top: 15px;
    width: 2px;
    height: calc(100% + 10px);
    background-color: #dee2e6;
}

.timeline-item:last-child:before {
    display: none;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin-bottom: 5px;
    color: #495057;
}

.timeline-text {
    margin-bottom: 5px;
    color: #6c757d;
}
</style>

<?php include '../includes/footer.php'; ?>
