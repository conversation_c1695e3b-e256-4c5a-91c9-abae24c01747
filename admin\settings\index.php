<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has super admin role
requireRole(['super_admin']);

$errors = [];
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['update_company'])) {
        // Update company settings
        $company_name = sanitize($_POST['company_name']);
        $address = sanitize($_POST['address']);
        $phone = sanitize($_POST['phone']);
        $email = sanitize($_POST['email']);
        $gst_number = sanitize($_POST['gst_number']);
        $website = sanitize($_POST['website']);
        
        try {
            $db->query("UPDATE company_settings SET 
                       company_name = :company_name,
                       address = :address,
                       phone = :phone,
                       email = :email,
                       gst_number = :gst_number,
                       website = :website,
                       updated_at = NOW()
                       WHERE id = 1");
            
            $db->bind(':company_name', $company_name);
            $db->bind(':address', $address);
            $db->bind(':phone', $phone);
            $db->bind(':email', $email);
            $db->bind(':gst_number', $gst_number);
            $db->bind(':website', $website);
            
            $db->execute();
            
            logActivity($_SESSION['user_id'], 'settings_updated', 'Updated company settings');
            $success = 'Company settings updated successfully!';
            
        } catch (Exception $e) {
            $errors[] = 'Error updating company settings: ' . $e->getMessage();
        }
    }
    
    if (isset($_POST['update_system'])) {
        // Update system settings
        $default_gas_price = (float)$_POST['default_gas_price'];
        $low_stock_threshold = (int)$_POST['low_stock_threshold'];
        $inspection_reminder_days = (int)$_POST['inspection_reminder_days'];
        $auto_assign_orders = isset($_POST['auto_assign_orders']) ? 1 : 0;
        $whatsapp_notifications = isset($_POST['whatsapp_notifications']) ? 1 : 0;
        $email_notifications = isset($_POST['email_notifications']) ? 1 : 0;
        
        try {
            $db->query("UPDATE system_settings SET 
                       default_gas_price = :default_gas_price,
                       low_stock_threshold = :low_stock_threshold,
                       inspection_reminder_days = :inspection_reminder_days,
                       auto_assign_orders = :auto_assign_orders,
                       whatsapp_notifications = :whatsapp_notifications,
                       email_notifications = :email_notifications,
                       updated_at = NOW()
                       WHERE id = 1");
            
            $db->bind(':default_gas_price', $default_gas_price);
            $db->bind(':low_stock_threshold', $low_stock_threshold);
            $db->bind(':inspection_reminder_days', $inspection_reminder_days);
            $db->bind(':auto_assign_orders', $auto_assign_orders);
            $db->bind(':whatsapp_notifications', $whatsapp_notifications);
            $db->bind(':email_notifications', $email_notifications);
            
            $db->execute();
            
            logActivity($_SESSION['user_id'], 'settings_updated', 'Updated system settings');
            $success = 'System settings updated successfully!';
            
        } catch (Exception $e) {
            $errors[] = 'Error updating system settings: ' . $e->getMessage();
        }
    }
}

// Get current settings
$db->query("SELECT * FROM company_settings WHERE id = 1");
$company_settings = $db->single();

$db->query("SELECT * FROM system_settings WHERE id = 1");
$system_settings = $db->single();

// If no settings exist, create default ones
if (!$company_settings) {
    $db->query("INSERT INTO company_settings (id, company_name, created_at) VALUES (1, 'Sony Enterprises', NOW())");
    $db->execute();
    
    $db->query("SELECT * FROM company_settings WHERE id = 1");
    $company_settings = $db->single();
}

if (!$system_settings) {
    $db->query("INSERT INTO system_settings (id, default_gas_price, low_stock_threshold, inspection_reminder_days, created_at) 
               VALUES (1, 500.00, 10, 30, NOW())");
    $db->execute();
    
    $db->query("SELECT * FROM system_settings WHERE id = 1");
    $system_settings = $db->single();
}

$page_title = 'System Settings';
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-cog"></i> System Settings</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="backup.php" class="btn btn-sm btn-warning">
                            <i class="fas fa-database"></i> Backup Database
                        </a>
                        <a href="users.php" class="btn btn-sm btn-info">
                            <i class="fas fa-users-cog"></i> Manage Users
                        </a>
                    </div>
                </div>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <!-- Settings Tabs -->
            <div class="card shadow">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="company-tab" data-bs-toggle="tab" data-bs-target="#company" type="button" role="tab">
                                <i class="fas fa-building"></i> Company Settings
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                                <i class="fas fa-cogs"></i> System Settings
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab">
                                <i class="fas fa-bell"></i> Notifications
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                                <i class="fas fa-shield-alt"></i> Security
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="settingsTabsContent">
                        <!-- Company Settings Tab -->
                        <div class="tab-pane fade show active" id="company" role="tabpanel">
                            <form method="POST" action="">
                                <input type="hidden" name="update_company" value="1">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="company_name" class="form-label">Company Name</label>
                                        <input type="text" class="form-control" id="company_name" name="company_name" 
                                               value="<?php echo htmlspecialchars($company_settings->company_name ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($company_settings->phone ?? ''); ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($company_settings->email ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="website" class="form-label">Website</label>
                                        <input type="url" class="form-control" id="website" name="website" 
                                               value="<?php echo htmlspecialchars($company_settings->website ?? ''); ?>">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($company_settings->address ?? ''); ?></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="gst_number" class="form-label">GST Number</label>
                                        <input type="text" class="form-control" id="gst_number" name="gst_number" 
                                               value="<?php echo htmlspecialchars($company_settings->gst_number ?? ''); ?>"
                                               pattern="[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}">
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Company Settings
                                </button>
                            </form>
                        </div>

                        <!-- System Settings Tab -->
                        <div class="tab-pane fade" id="system" role="tabpanel">
                            <form method="POST" action="">
                                <input type="hidden" name="update_system" value="1">
                                
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="default_gas_price" class="form-label">Default Gas Price (₹)</label>
                                        <input type="number" class="form-control" id="default_gas_price" name="default_gas_price" 
                                               value="<?php echo $system_settings->default_gas_price ?? 500; ?>" 
                                               step="0.01" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="low_stock_threshold" class="form-label">Low Stock Threshold</label>
                                        <input type="number" class="form-control" id="low_stock_threshold" name="low_stock_threshold" 
                                               value="<?php echo $system_settings->low_stock_threshold ?? 10; ?>" 
                                               min="1">
                                        <div class="form-text">Alert when cylinder count falls below this number</div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="inspection_reminder_days" class="form-label">Inspection Reminder (Days)</label>
                                        <input type="number" class="form-control" id="inspection_reminder_days" name="inspection_reminder_days" 
                                               value="<?php echo $system_settings->inspection_reminder_days ?? 30; ?>" 
                                               min="1">
                                        <div class="form-text">Days before inspection due to send reminder</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="auto_assign_orders" name="auto_assign_orders" 
                                                   <?php echo ($system_settings->auto_assign_orders ?? 0) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="auto_assign_orders">
                                                Auto-assign Orders
                                            </label>
                                            <div class="form-text">Automatically assign orders to available staff</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="whatsapp_notifications" name="whatsapp_notifications" 
                                                   <?php echo ($system_settings->whatsapp_notifications ?? 1) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="whatsapp_notifications">
                                                WhatsApp Notifications
                                            </label>
                                            <div class="form-text">Send notifications via WhatsApp</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" 
                                                   <?php echo ($system_settings->email_notifications ?? 0) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="email_notifications">
                                                Email Notifications
                                            </label>
                                            <div class="form-text">Send notifications via email</div>
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update System Settings
                                </button>
                            </form>
                        </div>

                        <!-- Notifications Tab -->
                        <div class="tab-pane fade" id="notifications" role="tabpanel">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Notification Settings</h6>
                                <p class="mb-0">Configure when and how notifications are sent to users.</p>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <h6>WhatsApp Notifications</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="notify_order_created" checked>
                                        <label class="form-check-label" for="notify_order_created">Order Created</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="notify_order_dispatched" checked>
                                        <label class="form-check-label" for="notify_order_dispatched">Order Dispatched</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="notify_payment_received" checked>
                                        <label class="form-check-label" for="notify_payment_received">Payment Received</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="notify_inspection_due" checked>
                                        <label class="form-check-label" for="notify_inspection_due">Inspection Due</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>System Alerts</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="alert_low_stock" checked>
                                        <label class="form-check-label" for="alert_low_stock">Low Stock Alert</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="alert_tank_low" checked>
                                        <label class="form-check-label" for="alert_tank_low">Tank Low Level</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="alert_overdue_payment" checked>
                                        <label class="form-check-label" for="alert_overdue_payment">Overdue Payments</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="alert_cylinder_damage" checked>
                                        <label class="form-check-label" for="alert_cylinder_damage">Cylinder Damage</label>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3">
                                <button type="button" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Notification Settings
                                </button>
                            </div>
                        </div>

                        <!-- Security Tab -->
                        <div class="tab-pane fade" id="security" role="tabpanel">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-shield-alt"></i> Security Settings</h6>
                                <p class="mb-0">Configure security policies and access controls.</p>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Password Policy</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="force_password_change" checked>
                                        <label class="form-check-label" for="force_password_change">Force password change on first login</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="password_expiry">
                                        <label class="form-check-label" for="password_expiry">Password expires after 90 days</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="strong_password" checked>
                                        <label class="form-check-label" for="strong_password">Require strong passwords</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Access Control</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="session_timeout" checked>
                                        <label class="form-check-label" for="session_timeout">Auto logout after 30 minutes</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="login_attempts">
                                        <label class="form-check-label" for="login_attempts">Lock account after 5 failed attempts</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="activity_logging" checked>
                                        <label class="form-check-label" for="activity_logging">Log all user activities</label>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3">
                                <button type="button" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Security Settings
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">System Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>System Version:</strong></td>
                                            <td>v2.0.0</td>
                                        </tr>
                                        <tr>
                                            <td><strong>PHP Version:</strong></td>
                                            <td><?php echo phpversion(); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Database:</strong></td>
                                            <td>MySQL <?php echo $db->getConnection()->getAttribute(PDO::ATTR_SERVER_VERSION); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Server:</strong></td>
                                            <td><?php echo $_SERVER['SERVER_SOFTWARE']; ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>Last Backup:</strong></td>
                                            <td>
                                                <?php
                                                // Check for last backup
                                                $backup_file = '../../backups/latest_backup.sql';
                                                if (file_exists($backup_file)) {
                                                    echo date('d-m-Y H:i', filemtime($backup_file));
                                                } else {
                                                    echo '<span class="text-warning">No backup found</span>';
                                                }
                                                ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Users:</strong></td>
                                            <td>
                                                <?php
                                                $db->query("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
                                                echo $db->single()->count;
                                                ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Clients:</strong></td>
                                            <td>
                                                <?php
                                                $db->query("SELECT COUNT(*) as count FROM clients WHERE is_active = 1");
                                                echo $db->single()->count;
                                                ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Cylinders:</strong></td>
                                            <td>
                                                <?php
                                                $db->query("SELECT COUNT(*) as count FROM cylinders WHERE is_active = 1");
                                                echo $db->single()->count;
                                                ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>
