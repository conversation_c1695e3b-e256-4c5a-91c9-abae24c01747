<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

$order_cylinder_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$order_id = isset($_GET['order_id']) ? (int)$_GET['order_id'] : 0;

// Handle AJAX request
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['order_cylinder_id'])) {
    header('Content-Type: application/json');
    
    $order_cylinder_id = (int)$_POST['order_cylinder_id'];
    
    try {
        // Get order cylinder details
        $db->query("SELECT oc.*, o.order_number, o.status, c.cylinder_code 
                   FROM order_cylinders oc
                   JOIN orders o ON oc.order_id = o.id
                   JOIN cylinders c ON oc.cylinder_id = c.id
                   WHERE oc.id = :id");
        $db->bind(':id', $order_cylinder_id);
        $order_cylinder = $db->single();
        
        if (!$order_cylinder) {
            echo json_encode(['success' => false, 'message' => 'Order cylinder not found.']);
            exit();
        }
        
        // Check if cylinder can be removed (not filled yet)
        if ($order_cylinder->filled_at) {
            echo json_encode(['success' => false, 'message' => 'Cannot remove cylinder that has already been filled.']);
            exit();
        }
        
        // Check if order allows cylinder removal
        if (!in_array($order_cylinder->status, ['pending', 'assigned'])) {
            echo json_encode(['success' => false, 'message' => 'Cylinders can only be removed from pending or assigned orders.']);
            exit();
        }
        
        $db->beginTransaction();
        
        // Remove cylinder from order
        $db->query("DELETE FROM order_cylinders WHERE id = :id");
        $db->bind(':id', $order_cylinder_id);
        $db->execute();
        
        // Update cylinder status back to available
        $db->query("UPDATE cylinders SET status = 'available', current_client_id = NULL, updated_at = NOW() 
                   WHERE id = :id");
        $db->bind(':id', $order_cylinder->cylinder_id);
        $db->execute();
        
        // Log cylinder movement
        $db->query("INSERT INTO cylinder_movement_logs 
                   (cylinder_id, movement_type, order_id, notes, moved_by, movement_date) 
                   VALUES (:cylinder_id, 'inward', :order_id, :notes, :moved_by, NOW())");
        $db->bind(':cylinder_id', $order_cylinder->cylinder_id);
        $db->bind(':order_id', $order_cylinder->order_id);
        $db->bind(':notes', "Removed from order {$order_cylinder->order_number}");
        $db->bind(':moved_by', $_SESSION['user_id']);
        $db->execute();
        
        $db->commit();
        
        // Log activity
        logActivity($_SESSION['user_id'], 'cylinder_removed', 
                   "Removed cylinder {$order_cylinder->cylinder_code} from order {$order_cylinder->order_number}");
        
        echo json_encode(['success' => true, 'message' => 'Cylinder removed successfully.']);
        
    } catch (Exception $e) {
        $db->rollback();
        echo json_encode(['success' => false, 'message' => 'Error removing cylinder: ' . $e->getMessage()]);
    }
    
    exit();
}

// Handle GET request (redirect method)
if (!$order_cylinder_id || !$order_id) {
    setFlashMessage('error', 'Invalid parameters.');
    header('Location: index.php');
    exit();
}

try {
    // Get order cylinder details
    $db->query("SELECT oc.*, o.order_number, o.status, c.cylinder_code 
               FROM order_cylinders oc
               JOIN orders o ON oc.order_id = o.id
               JOIN cylinders c ON oc.cylinder_id = c.id
               WHERE oc.id = :id AND oc.order_id = :order_id");
    $db->bind(':id', $order_cylinder_id);
    $db->bind(':order_id', $order_id);
    $order_cylinder = $db->single();
    
    if (!$order_cylinder) {
        setFlashMessage('error', 'Order cylinder not found.');
        header('Location: view.php?id=' . $order_id);
        exit();
    }
    
    // Check if cylinder can be removed (not filled yet)
    if ($order_cylinder->filled_at) {
        setFlashMessage('error', 'Cannot remove cylinder that has already been filled.');
        header('Location: view.php?id=' . $order_id);
        exit();
    }
    
    // Check if order allows cylinder removal
    if (!in_array($order_cylinder->status, ['pending', 'assigned'])) {
        setFlashMessage('error', 'Cylinders can only be removed from pending or assigned orders.');
        header('Location: view.php?id=' . $order_id);
        exit();
    }
    
    $db->beginTransaction();
    
    // Remove cylinder from order
    $db->query("DELETE FROM order_cylinders WHERE id = :id");
    $db->bind(':id', $order_cylinder_id);
    $db->execute();
    
    // Update cylinder status back to available
    $db->query("UPDATE cylinders SET status = 'available', current_client_id = NULL, updated_at = NOW() 
               WHERE id = :id");
    $db->bind(':id', $order_cylinder->cylinder_id);
    $db->execute();
    
    // Log cylinder movement
    $db->query("INSERT INTO cylinder_movement_logs 
               (cylinder_id, movement_type, order_id, notes, moved_by, movement_date) 
               VALUES (:cylinder_id, 'inward', :order_id, :notes, :moved_by, NOW())");
    $db->bind(':cylinder_id', $order_cylinder->cylinder_id);
    $db->bind(':order_id', $order_id);
    $db->bind(':notes', "Removed from order {$order_cylinder->order_number}");
    $db->bind(':moved_by', $_SESSION['user_id']);
    $db->execute();
    
    $db->commit();
    
    // Log activity
    logActivity($_SESSION['user_id'], 'cylinder_removed', 
               "Removed cylinder {$order_cylinder->cylinder_code} from order {$order_cylinder->order_number}");
    
    setFlashMessage('success', "Cylinder {$order_cylinder->cylinder_code} removed from order successfully.");
    
} catch (Exception $e) {
    $db->rollback();
    setFlashMessage('error', 'Error removing cylinder: ' . $e->getMessage());
}

header('Location: view.php?id=' . $order_id);
exit();
?>
