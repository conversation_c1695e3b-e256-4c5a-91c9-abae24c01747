<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and has appropriate role
requireRole(['super_admin', 'office_admin', 'accountant']);

$page_title = 'Reports & Analytics';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-chart-bar"></i> Reports & Analytics</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print"></i> Print Page
                        </button>
                    </div>
                </div>
            </div>

            <!-- Report Categories -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Available Reports</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- Operational Reports -->
                                <div class="col-md-4 mb-4">
                                    <h6 class="text-primary"><i class="fas fa-cogs"></i> Operational Reports</h6>
                                    <div class="list-group">
                                        <a href="cylinder_register.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-fire-extinguisher"></i> Cylinder Register
                                        </a>
                                        <a href="tank_usage.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-gas-pump"></i> Tank Usage Report
                                        </a>
                                        <a href="wastage_summary.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-exclamation-triangle"></i> Wastage Summary
                                        </a>
                                        <a href="inspection_due.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-calendar-check"></i> Inspection Due Report
                                        </a>
                                        <a href="order_status.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-clipboard-list"></i> Order Status Report
                                        </a>
                                    </div>
                                </div>

                                <!-- Financial Reports -->
                                <div class="col-md-4 mb-4">
                                    <h6 class="text-success"><i class="fas fa-rupee-sign"></i> Financial Reports</h6>
                                    <div class="list-group">
                                        <a href="profit_loss.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-chart-line"></i> Profit & Loss Statement
                                        </a>
                                        <a href="sales_report.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-shopping-cart"></i> Sales Report
                                        </a>
                                        <a href="payment_summary.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-credit-card"></i> Payment Summary
                                        </a>
                                        <a href="outstanding_dues.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-exclamation-circle"></i> Outstanding Dues
                                        </a>
                                        <a href="gst_report.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-file-invoice"></i> GST Report
                                        </a>
                                    </div>
                                </div>

                                <!-- Client Reports -->
                                <div class="col-md-4 mb-4">
                                    <h6 class="text-info"><i class="fas fa-users"></i> Client Reports</h6>
                                    <div class="list-group">
                                        <a href="client_ledger.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-book"></i> Client Ledger
                                        </a>
                                        <a href="client_analysis.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-chart-pie"></i> Client Analysis
                                        </a>
                                        <a href="delivery_report.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-truck"></i> Delivery Report
                                        </a>
                                        <a href="client_feedback.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-comments"></i> Client Feedback
                                        </a>
                                        <a href="special_pricing.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-tags"></i> Special Pricing Report
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Analytics Dashboard -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Quick Analytics</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- Today's Summary -->
                                <div class="col-md-3">
                                    <div class="card border-left-primary h-100">
                                        <div class="card-body">
                                            <h6 class="text-primary">Today's Orders</h6>
                                            <?php
                                            $db->query("SELECT COUNT(*) as count, SUM(total_amount) as amount FROM orders WHERE DATE(created_at) = CURDATE()");
                                            $today_orders = $db->single();
                                            ?>
                                            <h4><?php echo $today_orders->count; ?></h4>
                                            <small class="text-muted">Value: <?php echo formatCurrency($today_orders->amount ?? 0); ?></small>
                                        </div>
                                    </div>
                                </div>

                                <!-- This Month's Summary -->
                                <div class="col-md-3">
                                    <div class="card border-left-success h-100">
                                        <div class="card-body">
                                            <h6 class="text-success">This Month</h6>
                                            <?php
                                            $db->query("SELECT COUNT(*) as count, SUM(total_amount) as amount FROM orders WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())");
                                            $month_orders = $db->single();
                                            ?>
                                            <h4><?php echo $month_orders->count; ?></h4>
                                            <small class="text-muted">Value: <?php echo formatCurrency($month_orders->amount ?? 0); ?></small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Active Cylinders -->
                                <div class="col-md-3">
                                    <div class="card border-left-info h-100">
                                        <div class="card-body">
                                            <h6 class="text-info">Active Cylinders</h6>
                                            <?php
                                            $db->query("SELECT COUNT(*) as count FROM cylinders WHERE is_active = 1 AND status != 'damaged'");
                                            $active_cylinders = $db->single();
                                            ?>
                                            <h4><?php echo $active_cylinders->count; ?></h4>
                                            <small class="text-muted">In circulation</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Outstanding Amount -->
                                <div class="col-md-3">
                                    <div class="card border-left-warning h-100">
                                        <div class="card-body">
                                            <h6 class="text-warning">Outstanding</h6>
                                            <?php
                                            $db->query("SELECT SUM(balance_amount) as amount FROM invoices WHERE status != 'paid'");
                                            $outstanding = $db->single();
                                            ?>
                                            <h4><?php echo formatCurrency($outstanding->amount ?? 0); ?></h4>
                                            <small class="text-muted">Pending collection</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <!-- Monthly Sales Chart -->
                <div class="col-lg-6">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Monthly Sales Trend</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="monthlySalesChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Gas Type Distribution -->
                <div class="col-lg-6">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Gas Type Distribution</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="gasTypeChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Reports -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Recently Generated Reports</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Report Name</th>
                                            <th>Generated By</th>
                                            <th>Date</th>
                                            <th>Parameters</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // Get recent report generations (this would be from a report_logs table)
                                        $db->query("SELECT al.*, u.full_name 
                                                   FROM activity_logs al 
                                                   JOIN users u ON al.user_id = u.id 
                                                   WHERE al.action LIKE '%report%' 
                                                   ORDER BY al.created_at DESC LIMIT 10");
                                        $recent_reports = $db->resultset();
                                        ?>
                                        
                                        <?php if (!empty($recent_reports)): ?>
                                            <?php foreach ($recent_reports as $report): ?>
                                            <tr>
                                                <td><?php echo ucfirst(str_replace('_', ' ', $report->action)); ?></td>
                                                <td><?php echo htmlspecialchars($report->full_name); ?></td>
                                                <td><?php echo formatDateTime($report->created_at); ?></td>
                                                <td><small class="text-muted"><?php echo htmlspecialchars($report->description); ?></small></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="regenerateReport('<?php echo $report->action; ?>')">
                                                        <i class="fas fa-redo"></i> Regenerate
                                                    </button>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="5" class="text-center text-muted">No recent reports found</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Monthly Sales Chart
const monthlySalesCtx = document.getElementById('monthlySalesChart').getContext('2d');

<?php
// Get monthly sales data for the last 12 months
$monthly_data = [];
for ($i = 11; $i >= 0; $i--) {
    $month = date('Y-m', strtotime("-$i months"));
    $db->query("SELECT COALESCE(SUM(total_amount), 0) as amount FROM orders WHERE DATE_FORMAT(created_at, '%Y-%m') = :month");
    $db->bind(':month', $month);
    $result = $db->single();
    $monthly_data[] = [
        'month' => date('M Y', strtotime($month . '-01')),
        'amount' => $result->amount
    ];
}
?>

const monthlySalesChart = new Chart(monthlySalesCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode(array_column($monthly_data, 'month')); ?>,
        datasets: [{
            label: 'Sales Amount (₹)',
            data: <?php echo json_encode(array_column($monthly_data, 'amount')); ?>,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '₹' + value.toLocaleString();
                    }
                }
            }
        }
    }
});

// Gas Type Distribution Chart
const gasTypeCtx = document.getElementById('gasTypeChart').getContext('2d');

<?php
// Get gas type distribution
$db->query("SELECT g.name, g.code, COUNT(o.id) as order_count 
           FROM gas_types g 
           LEFT JOIN orders o ON g.id = o.gas_type_id 
           WHERE g.is_active = 1 
           GROUP BY g.id, g.name, g.code 
           ORDER BY order_count DESC");
$gas_type_data = $db->resultset();
?>

const gasTypeChart = new Chart(gasTypeCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode(array_map(function($item) { return $item->name; }, $gas_type_data)); ?>,
        datasets: [{
            data: <?php echo json_encode(array_map(function($item) { return $item->order_count; }, $gas_type_data)); ?>,
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

function regenerateReport(reportType) {
    // This would trigger the specific report generation
    alert('Regenerating ' + reportType.replace('_', ' ') + ' report...');
    // In a real implementation, this would make an AJAX call to generate the report
}
</script>

<?php include '../includes/footer.php'; ?>
