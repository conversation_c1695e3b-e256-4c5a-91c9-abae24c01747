<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

$order_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$order_id) {
    setFlashMessage('error', 'Invalid order ID.');
    header('Location: index.php');
    exit();
}

// Get order details
$db->query("SELECT o.*, c.name as client_name, g.name as gas_name 
           FROM orders o 
           JOIN clients c ON o.client_id = c.id 
           JOIN gas_types g ON o.gas_type_id = g.id 
           WHERE o.id = :id");
$db->bind(':id', $order_id);
$order = $db->single();

if (!$order) {
    setFlashMessage('error', 'Order not found.');
    header('Location: index.php');
    exit();
}

// Check if order can be assigned
if ($order->status != 'pending') {
    setFlashMessage('error', 'Only pending orders can be assigned to staff.');
    header('Location: view.php?id=' . $order_id);
    exit();
}

$errors = [];
$success = '';

// Get available staff members
$db->query("SELECT * FROM users 
           WHERE role IN ('refilling_staff', 'loading_staff') 
           AND is_active = 1 
           ORDER BY full_name");
$staff_members = $db->resultset();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $assigned_to = isset($_POST['assigned_to']) ? (int)$_POST['assigned_to'] : 0;
    $priority = isset($_POST['priority']) ? sanitize($_POST['priority']) : $order->priority;
    $notes = isset($_POST['notes']) ? sanitize($_POST['notes']) : '';
    
    // Validation
    if (!$assigned_to) {
        $errors[] = 'Please select a staff member to assign this order to.';
    } else {
        // Verify staff member exists and is active
        $db->query("SELECT * FROM users WHERE id = :id AND is_active = 1 AND role IN ('refilling_staff', 'loading_staff')");
        $db->bind(':id', $assigned_to);
        $staff = $db->single();
        
        if (!$staff) {
            $errors[] = 'Invalid staff member selected.';
        }
    }
    
    if (!in_array($priority, ['low', 'medium', 'high'])) {
        $errors[] = 'Invalid priority level.';
    }
    
    if (empty($errors)) {
        try {
            // Update order
            $db->query("UPDATE orders SET 
                       assigned_to = :assigned_to, 
                       priority = :priority, 
                       status = 'assigned',
                       notes = CONCAT(COALESCE(notes, ''), :notes),
                       updated_at = NOW()
                       WHERE id = :id");
            
            $db->bind(':assigned_to', $assigned_to);
            $db->bind(':priority', $priority);
            $db->bind(':notes', $notes ? "\n\nAssignment Notes: " . $notes : '');
            $db->bind(':id', $order_id);
            $db->execute();
            
            // Log activity
            logActivity($_SESSION['user_id'], 'order_assigned', 
                       "Assigned order {$order->order_number} to {$staff->full_name}");
            
            // Send notification to assigned staff (if WhatsApp integration is available)
            if ($staff->phone) {
                $message = "Hello {$staff->full_name}, you have been assigned a new order:\n\n";
                $message .= "Order: {$order->order_number}\n";
                $message .= "Client: {$order->client_name}\n";
                $message .= "Gas Type: {$order->gas_name}\n";
                $message .= "Quantity: {$order->quantity} cylinders\n";
                $message .= "Priority: " . ucfirst($priority) . "\n\n";
                $message .= "Please check your dashboard for details.\n\n";
                $message .= "- Sony Enterprises";
                
                // Store notification for later sending
                $db->query("INSERT INTO notifications (phone, message, type, created_at) 
                           VALUES (:phone, :message, 'order_assigned', NOW())");
                $db->bind(':phone', $staff->phone);
                $db->bind(':message', $message);
                $db->execute();
            }
            
            setFlashMessage('success', "Order successfully assigned to {$staff->full_name}!");
            header('Location: view.php?id=' . $order_id);
            exit();
            
        } catch (Exception $e) {
            $errors[] = 'Error assigning order: ' . $e->getMessage();
        }
    }
}

$page_title = 'Assign Order - ' . $order->order_number;
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-user-plus"></i> Assign Order</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="view.php?id=<?php echo $order_id; ?>" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Order
                        </a>
                        <a href="index.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-list"></i> All Orders
                        </a>
                    </div>
                </div>
            </div>

            <!-- Flash Messages -->
            <?php displayFlashMessages(); ?>

            <!-- Display Errors -->
            <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <div class="row">
                <!-- Order Information -->
                <div class="col-md-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Order Information</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Order Number:</strong></td>
                                    <td><?php echo $order->order_number; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Client:</strong></td>
                                    <td><?php echo $order->client_name; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Gas Type:</strong></td>
                                    <td><?php echo $order->gas_name; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Quantity:</strong></td>
                                    <td><?php echo $order->quantity; ?> cylinders</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Amount:</strong></td>
                                    <td><?php echo formatCurrency($order->total_amount); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Current Priority:</strong></td>
                                    <td>
                                        <span class="badge bg-<?php echo $order->priority == 'high' ? 'danger' : ($order->priority == 'medium' ? 'warning' : 'secondary'); ?>">
                                            <?php echo ucfirst($order->priority); ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Delivery Date:</strong></td>
                                    <td><?php echo $order->delivery_date ? formatDate($order->delivery_date) : 'Not specified'; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Assignment Form -->
                <div class="col-md-8">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Assign to Staff Member</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="assigned_to" class="form-label">Select Staff Member <span class="text-danger">*</span></label>
                                            <select class="form-select" id="assigned_to" name="assigned_to" required>
                                                <option value="">Choose staff member...</option>
                                                <?php foreach ($staff_members as $staff): ?>
                                                <option value="<?php echo $staff->id; ?>" 
                                                        <?php echo (isset($_POST['assigned_to']) && $_POST['assigned_to'] == $staff->id) ? 'selected' : ''; ?>>
                                                    <?php echo $staff->full_name; ?> 
                                                    (<?php echo ucfirst(str_replace('_', ' ', $staff->role)); ?>)
                                                    <?php if ($staff->phone): ?>
                                                    - <?php echo $staff->phone; ?>
                                                    <?php endif; ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <?php if (empty($staff_members)): ?>
                                            <div class="form-text text-warning">
                                                <i class="fas fa-exclamation-triangle"></i> No active staff members available. 
                                                <a href="../settings/users.php">Add staff members</a> first.
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="priority" class="form-label">Priority Level</label>
                                            <select class="form-select" id="priority" name="priority">
                                                <option value="low" <?php echo (isset($_POST['priority']) && $_POST['priority'] == 'low') || $order->priority == 'low' ? 'selected' : ''; ?>>Low</option>
                                                <option value="medium" <?php echo (isset($_POST['priority']) && $_POST['priority'] == 'medium') || $order->priority == 'medium' ? 'selected' : ''; ?>>Medium</option>
                                                <option value="high" <?php echo (isset($_POST['priority']) && $_POST['priority'] == 'high') || $order->priority == 'high' ? 'selected' : ''; ?>>High</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Assignment Notes</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="4" 
                                              placeholder="Add any special instructions or notes for the assigned staff member..."><?php echo isset($_POST['notes']) ? htmlspecialchars($_POST['notes']) : ''; ?></textarea>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="view.php?id=<?php echo $order_id; ?>" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary" <?php echo empty($staff_members) ? 'disabled' : ''; ?>>
                                        <i class="fas fa-user-plus"></i> Assign Order
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Staff Workload Information -->
                    <?php if (!empty($staff_members)): ?>
                    <div class="card shadow mt-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Staff Workload</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Staff Member</th>
                                            <th>Role</th>
                                            <th>Active Orders</th>
                                            <th>Pending Tasks</th>
                                            <th>Last Activity</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($staff_members as $staff): ?>
                                        <?php
                                        // Get staff workload
                                        $db->query("SELECT COUNT(*) as active_orders FROM orders 
                                                   WHERE assigned_to = :staff_id AND status IN ('assigned', 'refilling', 'loading')");
                                        $db->bind(':staff_id', $staff->id);
                                        $workload = $db->single();
                                        
                                        // Get pending tasks (cylinders to fill/load)
                                        $db->query("SELECT COUNT(*) as pending_tasks FROM order_cylinders oc
                                                   JOIN orders o ON oc.order_id = o.id
                                                   WHERE o.assigned_to = :staff_id 
                                                   AND ((o.status = 'refilling' AND oc.filled_at IS NULL) 
                                                        OR (o.status = 'loading' AND oc.loaded_at IS NULL))");
                                        $db->bind(':staff_id', $staff->id);
                                        $tasks = $db->single();
                                        ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo $staff->full_name; ?></strong>
                                                <?php if ($staff->phone): ?>
                                                <br><small class="text-muted"><?php echo $staff->phone; ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo ucfirst(str_replace('_', ' ', $staff->role)); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $workload->active_orders > 5 ? 'danger' : ($workload->active_orders > 2 ? 'warning' : 'success'); ?>">
                                                    <?php echo $workload->active_orders; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $tasks->pending_tasks > 10 ? 'danger' : ($tasks->pending_tasks > 5 ? 'warning' : 'success'); ?>">
                                                    <?php echo $tasks->pending_tasks; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo $staff->last_login ? formatDateTime($staff->last_login) : 'Never'; ?>
                                                </small>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i> 
                                    Consider staff workload when assigning orders. High workload staff may take longer to complete tasks.
                                </small>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
$(document).ready(function() {
    // Auto-select staff with lowest workload
    $('#auto-assign').click(function() {
        var lowestWorkload = 999;
        var bestStaff = null;
        
        $('table tbody tr').each(function() {
            var workload = parseInt($(this).find('td:nth-child(3) .badge').text());
            if (workload < lowestWorkload) {
                lowestWorkload = workload;
                bestStaff = $(this).find('td:first strong').text();
            }
        });
        
        if (bestStaff) {
            $('#assigned_to option').each(function() {
                if ($(this).text().includes(bestStaff)) {
                    $(this).prop('selected', true);
                    return false;
                }
            });
        }
    });
});
</script>

<?php include '../../includes/footer.php'; ?>
