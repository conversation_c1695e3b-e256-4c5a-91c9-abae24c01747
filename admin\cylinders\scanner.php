<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin', 'refilling_staff', 'loading_staff']);

$page_title = 'QR/Barcode Scanner';
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-qrcode"></i> QR/Barcode Scanner</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-primary" id="startScanBtn">
                            <i class="fas fa-camera"></i> Start Scanner
                        </button>
                        <button type="button" class="btn btn-sm btn-danger" id="stopScanBtn" style="display: none;">
                            <i class="fas fa-stop"></i> Stop Scanner
                        </button>
                    </div>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Cylinders
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Scanner</h6>
                        </div>
                        <div class="card-body">
                            <div id="scanner-container" class="text-center">
                                <div id="reader" style="width: 100%; max-width: 500px; margin: 0 auto;"></div>
                                <div id="scanner-status" class="mt-3">
                                    <p class="text-muted">Click "Start Scanner" to begin scanning QR codes or barcodes</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Manual Entry -->
                    <div class="card shadow mt-3">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Manual Entry</h6>
                        </div>
                        <div class="card-body">
                            <form id="manualSearchForm">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="manualCode" 
                                           placeholder="Enter cylinder code or barcode">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Search
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <!-- Scan Results -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Scan Results</h6>
                        </div>
                        <div class="card-body">
                            <div id="scan-results">
                                <div class="text-center text-muted">
                                    <i class="fas fa-search fa-3x mb-3"></i>
                                    <p>Scan a QR code or barcode to view cylinder details</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Scans -->
                    <div class="card shadow mt-3">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Scans</h6>
                        </div>
                        <div class="card-body">
                            <div id="recent-scans">
                                <p class="text-muted">No recent scans</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Modal -->
            <div class="modal fade" id="quickActionsModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Quick Actions</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div id="modal-cylinder-info"></div>
                            <div class="d-grid gap-2 mt-3">
                                <button type="button" class="btn btn-info" onclick="quickUpdateStatus('filled')">
                                    <i class="fas fa-fill-drip"></i> Mark as Filled
                                </button>
                                <button type="button" class="btn btn-primary" onclick="quickUpdateStatus('dispatched')">
                                    <i class="fas fa-truck"></i> Mark as Dispatched
                                </button>
                                <button type="button" class="btn btn-success" onclick="quickUpdateStatus('available')">
                                    <i class="fas fa-undo"></i> Mark as Available
                                </button>
                                <button type="button" class="btn btn-warning" onclick="quickUpdateStatus('maintenance')">
                                    <i class="fas fa-tools"></i> Send to Maintenance
                                </button>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <a href="#" id="viewCylinderBtn" class="btn btn-primary">View Details</a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
let html5QrcodeScanner = null;
let currentCylinderId = null;
let recentScans = [];

// Start scanner
document.getElementById('startScanBtn').addEventListener('click', function() {
    startScanner();
});

// Stop scanner
document.getElementById('stopScanBtn').addEventListener('click', function() {
    stopScanner();
});

// Manual search form
document.getElementById('manualSearchForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const code = document.getElementById('manualCode').value.trim();
    if (code) {
        searchCylinder(code);
    }
});

function startScanner() {
    const config = { 
        fps: 10, 
        qrbox: { width: 250, height: 250 },
        aspectRatio: 1.0,
        supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_QR_CODE, Html5QrcodeScanType.SCAN_TYPE_BARCODE]
    };
    
    html5QrcodeScanner = new Html5QrcodeScanner("reader", config, false);
    
    html5QrcodeScanner.render(onScanSuccess, onScanFailure);
    
    document.getElementById('startScanBtn').style.display = 'none';
    document.getElementById('stopScanBtn').style.display = 'inline-block';
    document.getElementById('scanner-status').innerHTML = '<p class="text-success">Scanner active - Point camera at QR code or barcode</p>';
}

function stopScanner() {
    if (html5QrcodeScanner) {
        html5QrcodeScanner.clear();
        html5QrcodeScanner = null;
    }
    
    document.getElementById('startScanBtn').style.display = 'inline-block';
    document.getElementById('stopScanBtn').style.display = 'none';
    document.getElementById('scanner-status').innerHTML = '<p class="text-muted">Scanner stopped</p>';
}

function onScanSuccess(decodedText, decodedResult) {
    console.log(`Code matched = ${decodedText}`, decodedResult);
    
    // Stop scanner temporarily to prevent multiple scans
    stopScanner();
    
    // Process the scanned code
    processScanResult(decodedText);
    
    // Restart scanner after 2 seconds
    setTimeout(() => {
        if (!html5QrcodeScanner) {
            startScanner();
        }
    }, 2000);
}

function onScanFailure(error) {
    // Handle scan failure, usually better to ignore and keep scanning
    console.warn(`Code scan error = ${error}`);
}

function processScanResult(code) {
    // Try to decode QR data first
    try {
        const qrData = JSON.parse(atob(code));
        if (qrData.type === 'cylinder' && qrData.id) {
            searchCylinderById(qrData.id);
            return;
        }
    } catch (e) {
        // Not a QR code, treat as barcode or cylinder code
    }
    
    // Search by code or barcode
    searchCylinder(code);
}

function searchCylinder(code) {
    showLoading('scan-results');
    
    fetch('search_cylinder.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `code=${encodeURIComponent(code)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayCylinderResult(data.cylinder);
            addToRecentScans(data.cylinder);
        } else {
            displayError(data.message);
        }
    })
    .catch(error => {
        displayError('Error searching cylinder');
    });
}

function searchCylinderById(id) {
    showLoading('scan-results');
    
    fetch('search_cylinder.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${id}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayCylinderResult(data.cylinder);
            addToRecentScans(data.cylinder);
        } else {
            displayError(data.message);
        }
    })
    .catch(error => {
        displayError('Error searching cylinder');
    });
}

function displayCylinderResult(cylinder) {
    currentCylinderId = cylinder.id;
    
    const statusClass = getStatusClass(cylinder.status);
    
    const html = `
        <div class="alert alert-success">
            <h6><i class="fas fa-check-circle"></i> Cylinder Found!</h6>
        </div>
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">${cylinder.cylinder_code}</h6>
                <p class="card-text">
                    <strong>Gas Type:</strong> ${cylinder.gas_name} (${cylinder.gas_code})<br>
                    <strong>Size:</strong> ${cylinder.size} kg<br>
                    <strong>Status:</strong> <span class="badge bg-${statusClass}">${cylinder.status.charAt(0).toUpperCase() + cylinder.status.slice(1)}</span><br>
                    <strong>Location:</strong> ${cylinder.location_name || '-'}<br>
                    <strong>Client:</strong> ${cylinder.client_name || '-'}
                </p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary" onclick="showQuickActions()">
                        <i class="fas fa-bolt"></i> Quick Actions
                    </button>
                    <a href="view.php?id=${cylinder.id}" class="btn btn-outline-info">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('scan-results').innerHTML = html;
}

function displayError(message) {
    const html = `
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle"></i> Not Found</h6>
            <p class="mb-0">${message}</p>
        </div>
    `;
    
    document.getElementById('scan-results').innerHTML = html;
}

function showLoading(elementId) {
    document.getElementById(elementId).innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Searching...</div>';
}

function getStatusClass(status) {
    const statusClasses = {
        'available': 'success',
        'filled': 'info',
        'dispatched': 'primary',
        'damaged': 'danger',
        'maintenance': 'warning',
        'lost': 'dark'
    };
    return statusClasses[status] || 'secondary';
}

function showQuickActions() {
    if (!currentCylinderId) return;
    
    // Get current cylinder info and show in modal
    fetch('search_cylinder.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${currentCylinderId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const cylinder = data.cylinder;
            document.getElementById('modal-cylinder-info').innerHTML = `
                <div class="text-center mb-3">
                    <h6>${cylinder.cylinder_code}</h6>
                    <p class="text-muted">${cylinder.gas_name} - ${cylinder.size} kg</p>
                    <span class="badge bg-${getStatusClass(cylinder.status)}">${cylinder.status.charAt(0).toUpperCase() + cylinder.status.slice(1)}</span>
                </div>
            `;
            
            document.getElementById('viewCylinderBtn').href = `view.php?id=${cylinder.id}`;
            
            new bootstrap.Modal(document.getElementById('quickActionsModal')).show();
        }
    });
}

function quickUpdateStatus(status) {
    if (!currentCylinderId) return;
    
    fetch('update_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `cylinder_id=${currentCylinderId}&status=${status}&notes=Updated via scanner`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('quickActionsModal')).hide();
            showNotification('Status updated successfully!', 'success');
            // Refresh the scan result
            searchCylinderById(currentCylinderId);
        } else {
            showNotification('Error updating status: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        showNotification('Error updating status', 'danger');
    });
}

function addToRecentScans(cylinder) {
    // Add to recent scans array
    recentScans.unshift({
        ...cylinder,
        scanned_at: new Date().toLocaleTimeString()
    });
    
    // Keep only last 5 scans
    recentScans = recentScans.slice(0, 5);
    
    // Update recent scans display
    updateRecentScansDisplay();
}

function updateRecentScansDisplay() {
    if (recentScans.length === 0) {
        document.getElementById('recent-scans').innerHTML = '<p class="text-muted">No recent scans</p>';
        return;
    }
    
    let html = '';
    recentScans.forEach(scan => {
        html += `
            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                <div>
                    <strong>${scan.cylinder_code}</strong><br>
                    <small class="text-muted">${scan.gas_name} - ${scan.scanned_at}</small>
                </div>
                <span class="badge bg-${getStatusClass(scan.status)}">${scan.status}</span>
            </div>
        `;
    });
    
    document.getElementById('recent-scans').innerHTML = html;
}

// Auto-focus manual input when page loads
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('manualCode').focus();
});
</script>

<?php include '../../includes/footer.php'; ?>
