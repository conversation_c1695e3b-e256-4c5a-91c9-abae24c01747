<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

$cylinder_id = isset($_POST['cylinder_id']) ? (int)$_POST['cylinder_id'] : 0;
$new_status = isset($_POST['status']) ? sanitize($_POST['status']) : '';
$notes = isset($_POST['notes']) ? sanitize($_POST['notes']) : '';

if (!$cylinder_id || empty($new_status)) {
    echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
    exit();
}

// Validate status
$valid_statuses = ['available', 'filled', 'dispatched', 'damaged', 'maintenance', 'lost'];
if (!in_array($new_status, $valid_statuses)) {
    echo json_encode(['success' => false, 'message' => 'Invalid status']);
    exit();
}

try {
    // Get current cylinder details
    $db->query("SELECT * FROM cylinders WHERE id = :id");
    $db->bind(':id', $cylinder_id);
    $cylinder = $db->single();
    
    if (!$cylinder) {
        echo json_encode(['success' => false, 'message' => 'Cylinder not found']);
        exit();
    }
    
    $old_status = $cylinder->status;
    
    // Update cylinder status
    $db->query("UPDATE cylinders SET status = :status, updated_at = NOW() WHERE id = :id");
    $db->bind(':status', $new_status);
    $db->bind(':id', $cylinder_id);
    $db->execute();
    
    // Update damage notes if status is damaged
    if ($new_status == 'damaged' && !empty($notes)) {
        $db->query("UPDATE cylinders SET damage_notes = :notes WHERE id = :id");
        $db->bind(':notes', $notes);
        $db->bind(':id', $cylinder_id);
        $db->execute();
    }
    
    // Log movement if status change involves location change
    $movement_type = '';
    $movement_notes = $notes;
    
    switch ($new_status) {
        case 'available':
            if ($old_status == 'dispatched') {
                $movement_type = 'inward';
                $movement_notes = 'Cylinder returned from client' . ($notes ? ' - ' . $notes : '');
            }
            break;
        case 'dispatched':
            $movement_type = 'outward';
            $movement_notes = 'Cylinder dispatched to client' . ($notes ? ' - ' . $notes : '');
            break;
        case 'maintenance':
            $movement_type = 'transfer';
            $movement_notes = 'Cylinder sent to maintenance' . ($notes ? ' - ' . $notes : '');
            break;
        case 'damaged':
            $movement_type = 'damage';
            $movement_notes = 'Cylinder marked as damaged' . ($notes ? ' - ' . $notes : '');
            break;
    }
    
    if ($movement_type) {
        $db->query("INSERT INTO cylinder_movement_logs (cylinder_id, movement_type, notes, moved_by, movement_date) 
                   VALUES (:cylinder_id, :movement_type, :notes, :moved_by, NOW())");
        $db->bind(':cylinder_id', $cylinder_id);
        $db->bind(':movement_type', $movement_type);
        $db->bind(':notes', $movement_notes);
        $db->bind(':moved_by', $_SESSION['user_id']);
        $db->execute();
    }
    
    // Log activity
    logActivity($_SESSION['user_id'], 'cylinder_status_updated', 
               "Updated cylinder {$cylinder->cylinder_code} status from {$old_status} to {$new_status}");
    
    echo json_encode(['success' => true, 'message' => 'Status updated successfully']);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error updating status: ' . $e->getMessage()]);
}
?>
