<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and has staff role
requireRole(['refilling_staff', 'loading_staff']);

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
    exit();
}

$order_id = isset($_POST['order_id']) ? (int)$_POST['order_id'] : 0;
$new_status = isset($_POST['status']) ? sanitize($_POST['status']) : '';

if (!$order_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid order ID.']);
    exit();
}

$valid_statuses = ['assigned', 'refilling', 'filled', 'loading', 'dispatched'];
if (!in_array($new_status, $valid_statuses)) {
    echo json_encode(['success' => false, 'message' => 'Invalid status.']);
    exit();
}

try {
    // Get order details and verify staff assignment
    $db->query("SELECT o.*, c.name as client_name, g.name as gas_name
               FROM orders o
               JOIN clients c ON o.client_id = c.id
               JOIN gas_types g ON o.gas_type_id = g.id
               WHERE o.id = :id");
    $db->bind(':id', $order_id);
    $order = $db->single();
    
    if (!$order) {
        echo json_encode(['success' => false, 'message' => 'Order not found.']);
        exit();
    }
    
    // Verify this staff member is assigned to this order
    if ($order->assigned_to != $_SESSION['user_id']) {
        echo json_encode(['success' => false, 'message' => 'You are not assigned to this order.']);
        exit();
    }
    
    // Validate status transition based on user role
    $user_role = $_SESSION['user_role'];
    
    if ($user_role == 'refilling_staff') {
        // Refilling staff can only change: assigned -> refilling, refilling -> filled
        if (!in_array($new_status, ['refilling', 'filled'])) {
            echo json_encode(['success' => false, 'message' => 'You can only update status to refilling or filled.']);
            exit();
        }
        
        if ($new_status == 'refilling' && $order->status != 'assigned') {
            echo json_encode(['success' => false, 'message' => 'Can only start refilling from assigned status.']);
            exit();
        }
        
        if ($new_status == 'filled' && $order->status != 'refilling') {
            echo json_encode(['success' => false, 'message' => 'Can only mark as filled from refilling status.']);
            exit();
        }
        
        // If marking as filled, verify all cylinders are actually filled
        if ($new_status == 'filled') {
            $db->query("SELECT COUNT(*) as total_cylinders,
                       COUNT(CASE WHEN filled_at IS NOT NULL THEN 1 END) as filled_cylinders
                       FROM order_cylinders WHERE order_id = :order_id");
            $db->bind(':order_id', $order_id);
            $progress = $db->single();
            
            if ($progress->filled_cylinders < $progress->total_cylinders) {
                echo json_encode(['success' => false, 'message' => 'Cannot mark order as filled. Some cylinders are still pending.']);
                exit();
            }
        }
    } elseif ($user_role == 'loading_staff') {
        // Loading staff can only change: filled -> loading, loading -> dispatched
        if (!in_array($new_status, ['loading', 'dispatched'])) {
            echo json_encode(['success' => false, 'message' => 'You can only update status to loading or dispatched.']);
            exit();
        }
        
        if ($new_status == 'loading' && $order->status != 'filled') {
            echo json_encode(['success' => false, 'message' => 'Can only start loading from filled status.']);
            exit();
        }
        
        if ($new_status == 'dispatched' && $order->status != 'loading') {
            echo json_encode(['success' => false, 'message' => 'Can only dispatch from loading status.']);
            exit();
        }
        
        // If dispatching, verify all cylinders are loaded
        if ($new_status == 'dispatched') {
            $db->query("SELECT COUNT(*) as total_cylinders,
                       COUNT(CASE WHEN loaded_at IS NOT NULL THEN 1 END) as loaded_cylinders
                       FROM order_cylinders WHERE order_id = :order_id");
            $db->bind(':order_id', $order_id);
            $progress = $db->single();
            
            if ($progress->loaded_cylinders < $progress->total_cylinders) {
                echo json_encode(['success' => false, 'message' => 'Cannot dispatch order. Some cylinders are not loaded yet.']);
                exit();
            }
        }
    }
    
    $db->beginTransaction();
    
    // Update order status
    $update_fields = ['status = :status', 'updated_at = NOW()'];
    $params = [':status' => $new_status, ':id' => $order_id];
    
    // Set completion timestamps
    if ($new_status == 'filled') {
        $update_fields[] = 'refill_completed_at = NOW()';
    } elseif ($new_status == 'loading') {
        $update_fields[] = 'loading_started_at = NOW()';
    } elseif ($new_status == 'dispatched') {
        $update_fields[] = 'dispatched_at = NOW()';
        $update_fields[] = 'loading_completed_at = NOW()';
    }
    
    $sql = "UPDATE orders SET " . implode(', ', $update_fields) . " WHERE id = :id";
    $db->query($sql);
    foreach ($params as $key => $value) {
        $db->bind($key, $value);
    }
    $db->execute();
    
    // Update cylinder statuses if needed
    if ($new_status == 'dispatched') {
        $db->query("UPDATE cylinders c
                   JOIN order_cylinders oc ON c.id = oc.cylinder_id
                   SET c.status = 'dispatched', c.updated_at = NOW()
                   WHERE oc.order_id = :order_id");
        $db->bind(':order_id', $order_id);
        $db->execute();
    }
    
    $db->commit();
    
    // Log activity
    $status_messages = [
        'refilling' => 'Started refilling process',
        'filled' => 'Completed refilling process',
        'loading' => 'Started loading process',
        'dispatched' => 'Dispatched order'
    ];
    
    $activity_message = $status_messages[$new_status] ?? "Updated status to $new_status";
    logActivity($_SESSION['user_id'], 'order_status_updated', 
               "$activity_message for order {$order->order_number}");
    
    // Prepare notification message for client (if applicable)
    $notification_message = '';
    if ($new_status == 'filled') {
        $notification_message = "Good news! Your order {$order->order_number} has been filled and is ready for loading.";
    } elseif ($new_status == 'dispatched') {
        $notification_message = "Your order {$order->order_number} has been dispatched and is on the way!";
    }
    
    // Store notification for later sending
    if ($notification_message) {
        $db->query("SELECT phone FROM clients WHERE id = :client_id");
        $db->bind(':client_id', $order->client_id);
        $client = $db->single();
        
        if ($client && $client->phone) {
            $db->query("INSERT INTO notifications (phone, message, type, created_at) 
                       VALUES (:phone, :message, :type, NOW())");
            $db->bind(':phone', $client->phone);
            $db->bind(':message', $notification_message . "\n\n- Sony Enterprises");
            $db->bind(':type', 'order_status_update');
            $db->execute();
        }
    }
    
    echo json_encode([
        'success' => true, 
        'message' => "Order status updated to " . ucfirst($new_status) . " successfully!",
        'new_status' => $new_status
    ]);
    
} catch (Exception $e) {
    $db->rollback();
    echo json_encode(['success' => false, 'message' => 'Error updating order status: ' . $e->getMessage()]);
}
?>
