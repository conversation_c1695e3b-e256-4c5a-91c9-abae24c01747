<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

$cylinder_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$cylinder_id) {
    setFlashMessage('error', 'Invalid cylinder ID.');
    header('Location: index.php');
    exit();
}

// Get cylinder details
$db->query("SELECT c.*, g.name as gas_name, g.code as gas_code, l.name as location_name, 
           cl.name as client_name, cl.client_code, cl.phone as client_phone
           FROM cylinders c 
           JOIN gas_types g ON c.gas_type_id = g.id 
           LEFT JOIN locations l ON c.location_id = l.id 
           LEFT JOIN clients cl ON c.current_client_id = cl.id 
           WHERE c.id = :id");
$db->bind(':id', $cylinder_id);
$cylinder = $db->single();

if (!$cylinder) {
    setFlashMessage('error', 'Cylinder not found.');
    header('Location: index.php');
    exit();
}

// Get movement history
$db->query("SELECT cml.*, l1.name as from_location, l2.name as to_location, 
           cl.name as client_name, cl.client_code, u.full_name as moved_by_name
           FROM cylinder_movement_logs cml 
           LEFT JOIN locations l1 ON cml.from_location_id = l1.id 
           LEFT JOIN locations l2 ON cml.to_location_id = l2.id 
           LEFT JOIN clients cl ON cml.client_id = cl.id 
           LEFT JOIN users u ON cml.moved_by = u.id 
           WHERE cml.cylinder_id = :cylinder_id 
           ORDER BY cml.movement_date DESC LIMIT 20");
$db->bind(':cylinder_id', $cylinder_id);
$movement_history = $db->resultset();

// Get order history
$db->query("SELECT oc.*, o.order_number, o.status as order_status, o.created_at as order_date,
           cl.name as client_name, cl.client_code, u1.full_name as filled_by_name, u2.full_name as loaded_by_name
           FROM order_cylinders oc 
           JOIN orders o ON oc.order_id = o.id 
           JOIN clients cl ON o.client_id = cl.id 
           LEFT JOIN users u1 ON oc.filled_by = u1.id 
           LEFT JOIN users u2 ON oc.loaded_by = u2.id 
           WHERE oc.cylinder_id = :cylinder_id 
           ORDER BY o.created_at DESC LIMIT 10");
$db->bind(':cylinder_id', $cylinder_id);
$order_history = $db->resultset();

$page_title = 'Cylinder Details - ' . $cylinder->cylinder_code;
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-fire-extinguisher"></i> Cylinder Details</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="edit.php?id=<?php echo $cylinder->id; ?>" class="btn btn-sm btn-warning">
                            <i class="fas fa-edit"></i> Edit Cylinder
                        </a>
                        <a href="qr.php?id=<?php echo $cylinder->id; ?>" class="btn btn-sm btn-info">
                            <i class="fas fa-qrcode"></i> QR Code
                        </a>
                        <button type="button" class="btn btn-sm btn-success" onclick="printDiv('cylinderDetails')">
                            <i class="fas fa-print"></i> Print Label
                        </button>
                    </div>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Cylinders
                    </a>
                </div>
            </div>

            <!-- Cylinder Information Card -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card shadow" id="cylinderDetails">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                Cylinder Information - <?php echo $cylinder->cylinder_code; ?>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Cylinder Code:</strong></td>
                                            <td><?php echo $cylinder->cylinder_code; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Gas Type:</strong></td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo $cylinder->gas_code; ?></span>
                                                <?php echo $cylinder->gas_name; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Size:</strong></td>
                                            <td><?php echo $cylinder->size; ?> kg</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $cylinder->status == 'available' ? 'success' : 
                                                        ($cylinder->status == 'filled' ? 'info' : 
                                                        ($cylinder->status == 'dispatched' ? 'primary' : 
                                                        ($cylinder->status == 'damaged' ? 'danger' : 'warning'))); 
                                                ?>">
                                                    <?php echo ucfirst($cylinder->status); ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Barcode:</strong></td>
                                            <td><?php echo htmlspecialchars($cylinder->barcode ?? '-'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Empty Weight:</strong></td>
                                            <td><?php echo $cylinder->weight_empty ? $cylinder->weight_empty . ' kg' : '-'; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Full Weight:</strong></td>
                                            <td><?php echo $cylinder->weight_full ? $cylinder->weight_full . ' kg' : '-'; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Pressure Rating:</strong></td>
                                            <td><?php echo $cylinder->pressure_rating ? $cylinder->pressure_rating . ' bar' : '-'; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Current Location:</strong></td>
                                            <td><?php echo htmlspecialchars($cylinder->location_name ?? '-'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>QR Code:</strong></td>
                                            <td>
                                                <?php if (!empty($cylinder->qr_code)): ?>
                                                    <span class="badge bg-success">Available</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Not Generated</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Dates Section -->
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6 class="text-primary">Important Dates</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <strong>Manufacture Date:</strong><br>
                                            <?php echo $cylinder->manufacture_date ? formatDate($cylinder->manufacture_date) : '-'; ?>
                                            <?php if ($cylinder->manufacture_date): ?>
                                                <br><small class="text-muted">Age: <?php echo calculateCylinderAge($cylinder->manufacture_date); ?> years</small>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Last Inspection:</strong><br>
                                            <?php echo $cylinder->last_inspection_date ? formatDate($cylinder->last_inspection_date) : '-'; ?>
                                            <?php if ($cylinder->last_inspection_date): ?>
                                                <?php if (needsInspection($cylinder->last_inspection_date)): ?>
                                                    <br><span class="badge bg-warning">Inspection Due</span>
                                                <?php else: ?>
                                                    <br><span class="badge bg-success">Current</span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <br><span class="badge bg-danger">Never Inspected</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Expiry Date:</strong><br>
                                            <?php echo $cylinder->expiry_date ? formatDate($cylinder->expiry_date) : '-'; ?>
                                            <?php if ($cylinder->expiry_date): ?>
                                                <?php if (strtotime($cylinder->expiry_date) < time()): ?>
                                                    <br><span class="badge bg-danger">Expired</span>
                                                <?php else: ?>
                                                    <br><span class="badge bg-success">Valid</span>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Current Client Section -->
                            <?php if ($cylinder->client_name): ?>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6 class="text-primary">Current Client</h6>
                                    <div class="alert alert-info">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <strong><?php echo htmlspecialchars($cylinder->client_name); ?></strong>
                                                <br>Client Code: <?php echo $cylinder->client_code; ?>
                                            </div>
                                            <div class="col-md-4 text-end">
                                                <a href="<?php echo generateWhatsAppURL($cylinder->client_phone, 'Hello ' . $cylinder->client_name . ', regarding your cylinder ' . $cylinder->cylinder_code); ?>" 
                                                   target="_blank" class="btn btn-sm btn-success">
                                                    <i class="fab fa-whatsapp"></i> Contact Client
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Damage Notes -->
                            <?php if (!empty($cylinder->damage_notes)): ?>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6 class="text-danger">Damage Notes</h6>
                                    <div class="alert alert-danger">
                                        <?php echo nl2br(htmlspecialchars($cylinder->damage_notes)); ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <?php if ($cylinder->status == 'available'): ?>
                                    <button type="button" class="btn btn-info" onclick="updateStatus('filled')">
                                        <i class="fas fa-fill-drip"></i> Mark as Filled
                                    </button>
                                <?php endif; ?>
                                
                                <?php if ($cylinder->status == 'filled'): ?>
                                    <button type="button" class="btn btn-primary" onclick="updateStatus('dispatched')">
                                        <i class="fas fa-truck"></i> Mark as Dispatched
                                    </button>
                                <?php endif; ?>
                                
                                <?php if ($cylinder->status == 'dispatched'): ?>
                                    <button type="button" class="btn btn-success" onclick="updateStatus('available')">
                                        <i class="fas fa-undo"></i> Mark as Returned
                                    </button>
                                <?php endif; ?>
                                
                                <button type="button" class="btn btn-warning" onclick="updateStatus('maintenance')">
                                    <i class="fas fa-tools"></i> Send to Maintenance
                                </button>
                                
                                <button type="button" class="btn btn-danger" onclick="updateStatus('damaged')">
                                    <i class="fas fa-exclamation-triangle"></i> Mark as Damaged
                                </button>
                                
                                <hr>
                                
                                <a href="qr.php?id=<?php echo $cylinder->id; ?>" class="btn btn-outline-info">
                                    <i class="fas fa-qrcode"></i> View QR Code
                                </a>
                                
                                <button type="button" class="btn btn-outline-secondary" onclick="printDiv('cylinderDetails')">
                                    <i class="fas fa-print"></i> Print Details
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Inspection Alert -->
                    <?php if (needsInspection($cylinder->last_inspection_date)): ?>
                    <div class="card shadow mt-3 border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="m-0"><i class="fas fa-exclamation-triangle"></i> Inspection Required</h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-2">This cylinder requires inspection.</p>
                            <button type="button" class="btn btn-warning btn-sm" onclick="scheduleInspection()">
                                <i class="fas fa-calendar"></i> Schedule Inspection
                            </button>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- History Tabs -->
            <div class="card shadow">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="historyTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="movement-tab" data-bs-toggle="tab" data-bs-target="#movement" type="button" role="tab">
                                <i class="fas fa-exchange-alt"></i> Movement History
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders" type="button" role="tab">
                                <i class="fas fa-clipboard-list"></i> Order History
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="historyTabsContent">
                        <!-- Movement History Tab -->
                        <div class="tab-pane fade show active" id="movement" role="tabpanel">
                            <?php if (!empty($movement_history)): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Movement Type</th>
                                            <th>From</th>
                                            <th>To</th>
                                            <th>Client</th>
                                            <th>Moved By</th>
                                            <th>Notes</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($movement_history as $movement): ?>
                                        <tr>
                                            <td><?php echo formatDateTime($movement->movement_date); ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $movement->movement_type == 'inward' ? 'success' : 
                                                        ($movement->movement_type == 'outward' ? 'primary' : 'warning'); 
                                                ?>">
                                                    <?php echo ucfirst($movement->movement_type); ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($movement->from_location ?? '-'); ?></td>
                                            <td><?php echo htmlspecialchars($movement->to_location ?? '-'); ?></td>
                                            <td>
                                                <?php if ($movement->client_name): ?>
                                                    <?php echo htmlspecialchars($movement->client_name); ?>
                                                    <br><small class="text-muted"><?php echo $movement->client_code; ?></small>
                                                <?php else: ?>
                                                    -
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($movement->moved_by_name ?? '-'); ?></td>
                                            <td><?php echo htmlspecialchars($movement->notes ?? '-'); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="text-center text-muted">
                                <i class="fas fa-exchange-alt fa-3x mb-3"></i>
                                <p>No movement history found for this cylinder.</p>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Order History Tab -->
                        <div class="tab-pane fade" id="orders" role="tabpanel">
                            <?php if (!empty($order_history)): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Order #</th>
                                            <th>Client</th>
                                            <th>Order Date</th>
                                            <th>Filled Quantity</th>
                                            <th>Filled By</th>
                                            <th>Loaded By</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($order_history as $order): ?>
                                        <tr>
                                            <td><?php echo $order->order_number; ?></td>
                                            <td>
                                                <?php echo htmlspecialchars($order->client_name); ?>
                                                <br><small class="text-muted"><?php echo $order->client_code; ?></small>
                                            </td>
                                            <td><?php echo formatDate($order->order_date); ?></td>
                                            <td><?php echo $order->filled_quantity ? $order->filled_quantity . ' kg' : '-'; ?></td>
                                            <td><?php echo htmlspecialchars($order->filled_by_name ?? '-'); ?></td>
                                            <td><?php echo htmlspecialchars($order->loaded_by_name ?? '-'); ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $order->order_status == 'pending' ? 'warning' : 
                                                        ($order->order_status == 'dispatched' ? 'success' : 'info'); 
                                                ?>">
                                                    <?php echo ucfirst($order->order_status); ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="text-center text-muted">
                                <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                                <p>No order history found for this cylinder.</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Cylinder Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="statusForm">
                    <input type="hidden" id="newStatus" name="status">
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="confirmStatusUpdate()">Update Status</button>
            </div>
        </div>
    </div>
</div>

<script>
function updateStatus(status) {
    document.getElementById('newStatus').value = status;
    document.getElementById('notes').value = '';
    
    // Set modal title based on status
    const titles = {
        'filled': 'Mark Cylinder as Filled',
        'dispatched': 'Mark Cylinder as Dispatched',
        'available': 'Mark Cylinder as Available',
        'maintenance': 'Send Cylinder to Maintenance',
        'damaged': 'Mark Cylinder as Damaged'
    };
    
    document.querySelector('#statusModal .modal-title').textContent = titles[status] || 'Update Status';
    
    // Show modal
    new bootstrap.Modal(document.getElementById('statusModal')).show();
}

function confirmStatusUpdate() {
    const status = document.getElementById('newStatus').value;
    const notes = document.getElementById('notes').value;
    
    // Send AJAX request to update status
    fetch('update_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `cylinder_id=<?php echo $cylinder->id; ?>&status=${status}&notes=${encodeURIComponent(notes)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating status: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error updating status');
    });
    
    // Hide modal
    bootstrap.Modal.getInstance(document.getElementById('statusModal')).hide();
}

function scheduleInspection() {
    if (confirm('Schedule inspection for this cylinder?')) {
        // This would integrate with a scheduling system
        alert('Inspection scheduled. You will be notified when due.');
    }
}
</script>

<?php include '../../includes/footer.php'; ?>
