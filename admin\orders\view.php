<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

$order_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$order_id) {
    setFlashMessage('error', 'Invalid order ID.');
    header('Location: index.php');
    exit();
}

// Get order details with related information
$db->query("SELECT o.*, c.name as client_name, c.client_code, c.phone as client_phone, 
           c.email as client_email, c.address as client_address,
           g.name as gas_name, g.code as gas_code, g.price as gas_price,
           u1.full_name as created_by_name, u2.full_name as assigned_to_name,
           u2.phone as assigned_phone, u2.role as assigned_role
           FROM orders o 
           JOIN clients c ON o.client_id = c.id 
           JOIN gas_types g ON o.gas_type_id = g.id 
           LEFT JOIN users u1 ON o.created_by = u1.id 
           LEFT JOIN users u2 ON o.assigned_to = u2.id 
           WHERE o.id = :id");
$db->bind(':id', $order_id);
$order = $db->single();

if (!$order) {
    setFlashMessage('error', 'Order not found.');
    header('Location: index.php');
    exit();
}

// Get allocated cylinders for this order
$db->query("SELECT oc.*, c.cylinder_code, c.barcode, c.size, c.weight_empty, c.weight_full,
           c.status as cylinder_status, l.name as location_name,
           u1.full_name as filled_by_name, u2.full_name as loaded_by_name
           FROM order_cylinders oc
           JOIN cylinders c ON oc.cylinder_id = c.id
           LEFT JOIN locations l ON c.location_id = l.id
           LEFT JOIN users u1 ON oc.filled_by = u1.id
           LEFT JOIN users u2 ON oc.loaded_by = u2.id
           WHERE oc.order_id = :order_id
           ORDER BY oc.created_at");
$db->bind(':order_id', $order_id);
$allocated_cylinders = $db->resultset();

// Get order activity logs
$db->query("SELECT al.*, u.full_name as user_name
           FROM activity_logs al
           JOIN users u ON al.user_id = u.id
           WHERE al.details LIKE :order_ref
           ORDER BY al.created_at DESC");
$db->bind(':order_ref', '%' . $order->order_number . '%');
$activity_logs = $db->resultset();

// Calculate progress
$total_cylinders = count($allocated_cylinders);
$filled_cylinders = 0;
$loaded_cylinders = 0;

foreach ($allocated_cylinders as $cylinder) {
    if ($cylinder->filled_at) $filled_cylinders++;
    if ($cylinder->loaded_at) $loaded_cylinders++;
}

$page_title = 'Order Details - ' . $order->order_number;
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-clipboard-list"></i> Order Details</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="index.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Orders
                        </a>
                        <?php if ($order->status == 'pending'): ?>
                        <a href="assign.php?id=<?php echo $order->id; ?>" class="btn btn-sm btn-warning">
                            <i class="fas fa-user-plus"></i> Assign Staff
                        </a>
                        <a href="allocate_cylinders.php?id=<?php echo $order->id; ?>" class="btn btn-sm btn-info">
                            <i class="fas fa-gas-pump"></i> Allocate Cylinders
                        </a>
                        <?php endif; ?>
                        <a href="edit.php?id=<?php echo $order->id; ?>" class="btn btn-sm btn-secondary">
                            <i class="fas fa-edit"></i> Edit Order
                        </a>
                        <?php if (generateWhatsAppURL($order->client_phone, '')): ?>
                        <a href="<?php echo generateWhatsAppURL($order->client_phone, 'Hello ' . $order->client_name . ', regarding your order ' . $order->order_number . ' from Sony Enterprises.'); ?>" 
                           target="_blank" class="btn btn-sm btn-success">
                            <i class="fab fa-whatsapp"></i> WhatsApp Client
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Flash Messages -->
            <?php displayFlashMessages(); ?>

            <!-- Order Header -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">Order Information</h6>
                            <span class="badge bg-<?php 
                                echo $order->status == 'pending' ? 'warning' : 
                                    ($order->status == 'dispatched' ? 'success' : 
                                    ($order->status == 'cancelled' ? 'danger' : 'info')); 
                            ?> fs-6">
                                <?php echo ucfirst(str_replace('_', ' ', $order->status)); ?>
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Order Number:</strong></td>
                                            <td><?php echo $order->order_number; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Order Date:</strong></td>
                                            <td><?php echo formatDateTime($order->created_at); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Gas Type:</strong></td>
                                            <td><?php echo $order->gas_name; ?> (<?php echo $order->gas_code; ?>)</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Quantity:</strong></td>
                                            <td><?php echo $order->quantity; ?> cylinders</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Unit Price:</strong></td>
                                            <td><?php echo formatCurrency($order->unit_price); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Amount:</strong></td>
                                            <td><strong><?php echo formatCurrency($order->total_amount); ?></strong></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Priority:</strong></td>
                                            <td>
                                                <span class="badge bg-<?php echo $order->priority == 'high' ? 'danger' : ($order->priority == 'medium' ? 'warning' : 'secondary'); ?>">
                                                    <?php echo ucfirst($order->priority); ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Delivery Date:</strong></td>
                                            <td><?php echo $order->delivery_date ? formatDate($order->delivery_date) : 'Not specified'; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Created By:</strong></td>
                                            <td><?php echo $order->created_by_name; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Assigned To:</strong></td>
                                            <td>
                                                <?php if ($order->assigned_to_name): ?>
                                                    <?php echo $order->assigned_to_name; ?>
                                                    <small class="text-muted">(<?php echo ucfirst(str_replace('_', ' ', $order->assigned_role)); ?>)</small>
                                                <?php else: ?>
                                                    <span class="text-muted">Not assigned</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php if ($order->notes): ?>
                                        <tr>
                                            <td><strong>Special Instructions:</strong></td>
                                            <td><?php echo nl2br(htmlspecialchars($order->notes)); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Client Information</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td><?php echo $order->client_name; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Code:</strong></td>
                                    <td><?php echo $order->client_code; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>
                                        <?php echo $order->client_phone; ?>
                                        <a href="tel:<?php echo $order->client_phone; ?>" class="btn btn-sm btn-outline-primary ms-1">
                                            <i class="fas fa-phone"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php if ($order->client_email): ?>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>
                                        <a href="mailto:<?php echo $order->client_email; ?>"><?php echo $order->client_email; ?></a>
                                    </td>
                                </tr>
                                <?php endif; ?>
                                <?php if ($order->delivery_address): ?>
                                <tr>
                                    <td><strong>Delivery Address:</strong></td>
                                    <td><?php echo nl2br(htmlspecialchars($order->delivery_address)); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                            <div class="text-center">
                                <a href="../clients/view.php?id=<?php echo $order->client_id; ?>" class="btn btn-sm btn-primary">
                                    <i class="fas fa-user"></i> View Client Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Progress Tracking -->
            <?php if ($total_cylinders > 0): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Order Progress</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="progress-item">
                                        <div class="progress-circle <?php echo $order->status != 'pending' ? 'completed' : 'active'; ?>">
                                            <i class="fas fa-clipboard-list"></i>
                                        </div>
                                        <h6>Order Created</h6>
                                        <small class="text-muted"><?php echo formatDateTime($order->created_at); ?></small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="progress-item">
                                        <div class="progress-circle <?php echo in_array($order->status, ['refilling', 'filled', 'loading', 'dispatched', 'delivered']) ? 'completed' : ($order->status == 'assigned' ? 'active' : ''); ?>">
                                            <i class="fas fa-user-check"></i>
                                        </div>
                                        <h6>Staff Assigned</h6>
                                        <small class="text-muted">
                                            <?php echo $order->assigned_to_name ? $order->assigned_to_name : 'Pending'; ?>
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="progress-item">
                                        <div class="progress-circle <?php echo in_array($order->status, ['filled', 'loading', 'dispatched', 'delivered']) ? 'completed' : ($order->status == 'refilling' ? 'active' : ''); ?>">
                                            <i class="fas fa-gas-pump"></i>
                                        </div>
                                        <h6>Refilling</h6>
                                        <small class="text-muted">
                                            <?php echo $filled_cylinders; ?>/<?php echo $total_cylinders; ?> cylinders
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="progress-item">
                                        <div class="progress-circle <?php echo in_array($order->status, ['dispatched', 'delivered']) ? 'completed' : ($order->status == 'loading' ? 'active' : ''); ?>">
                                            <i class="fas fa-truck"></i>
                                        </div>
                                        <h6>Dispatch</h6>
                                        <small class="text-muted">
                                            <?php echo $order->dispatched_at ? formatDateTime($order->dispatched_at) : 'Pending'; ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Allocated Cylinders -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                Allocated Cylinders (<?php echo count($allocated_cylinders); ?>)
                            </h6>
                            <?php if ($order->status == 'pending' || $order->status == 'assigned'): ?>
                            <a href="allocate_cylinders.php?id=<?php echo $order->id; ?>" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> Allocate More
                            </a>
                            <?php endif; ?>
                        </div>
                        <div class="card-body">
                            <?php if (empty($allocated_cylinders)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-gas-pump fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No cylinders allocated yet</h5>
                                <p class="text-muted">Click "Allocate Cylinders" to assign cylinders to this order.</p>
                                <?php if ($order->status == 'pending' || $order->status == 'assigned'): ?>
                                <a href="allocate_cylinders.php?id=<?php echo $order->id; ?>" class="btn btn-primary">
                                    <i class="fas fa-gas-pump"></i> Allocate Cylinders
                                </a>
                                <?php endif; ?>
                            </div>
                            <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Cylinder Code</th>
                                            <th>Barcode</th>
                                            <th>Size</th>
                                            <th>Location</th>
                                            <th>Status</th>
                                            <th>Filled</th>
                                            <th>Loaded</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($allocated_cylinders as $cylinder): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo $cylinder->cylinder_code; ?></strong>
                                                <br><small class="text-muted">ID: <?php echo $cylinder->cylinder_id; ?></small>
                                            </td>
                                            <td><?php echo $cylinder->barcode ?: '-'; ?></td>
                                            <td><?php echo $cylinder->size; ?> kg</td>
                                            <td><?php echo $cylinder->location_name ?: '-'; ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $cylinder->cylinder_status == 'available' ? 'success' : 
                                                        ($cylinder->cylinder_status == 'filled' ? 'info' : 
                                                        ($cylinder->cylinder_status == 'dispatched' ? 'warning' : 'secondary')); 
                                                ?>">
                                                    <?php echo ucfirst($cylinder->cylinder_status); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($cylinder->filled_at): ?>
                                                    <span class="text-success">
                                                        <i class="fas fa-check"></i> <?php echo formatDateTime($cylinder->filled_at); ?>
                                                    </span>
                                                    <br><small class="text-muted">By: <?php echo $cylinder->filled_by_name; ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">Pending</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($cylinder->loaded_at): ?>
                                                    <span class="text-success">
                                                        <i class="fas fa-check"></i> <?php echo formatDateTime($cylinder->loaded_at); ?>
                                                    </span>
                                                    <br><small class="text-muted">By: <?php echo $cylinder->loaded_by_name; ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">Pending</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="../cylinders/view.php?id=<?php echo $cylinder->cylinder_id; ?>" 
                                                       class="btn btn-sm btn-outline-info" title="View Cylinder">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($order->status == 'pending' || $order->status == 'assigned'): ?>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="removeCylinder(<?php echo $cylinder->id; ?>)" title="Remove">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

        </main>
    </div>
</div>

<style>
.progress-item {
    text-align: center;
    position: relative;
}

.progress-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    font-size: 24px;
    border: 3px solid #dee2e6;
    background-color: #f8f9fa;
    color: #6c757d;
}

.progress-circle.active {
    border-color: #007bff;
    background-color: #007bff;
    color: white;
}

.progress-circle.completed {
    border-color: #28a745;
    background-color: #28a745;
    color: white;
}

.progress-item:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 30px;
    right: -50%;
    width: 100%;
    height: 3px;
    background-color: #dee2e6;
    z-index: -1;
}

.progress-item.completed:not(:last-child)::after {
    background-color: #28a745;
}
</style>

<script>
function removeCylinder(orderCylinderId) {
    if (confirm('Are you sure you want to remove this cylinder from the order?')) {
        // AJAX call to remove cylinder
        $.post('remove_cylinder.php', {
            order_cylinder_id: orderCylinderId
        }, function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error: ' + response.message);
            }
        }, 'json');
    }
}
</script>

<?php include '../../includes/footer.php'; ?>
