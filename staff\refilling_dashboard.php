<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and has refilling staff role
requireRole(['refilling_staff']);

$staff_id = $_SESSION['user_id'];

// Get assigned orders for this staff member
$db->query("SELECT o.*, c.name as client_name, c.phone as client_phone,
           g.name as gas_name, g.code as gas_code,
           (SELECT COUNT(*) FROM order_cylinders WHERE order_id = o.id) as total_cylinders,
           (SELECT COUNT(*) FROM order_cylinders WHERE order_id = o.id AND filled_at IS NOT NULL) as filled_cylinders
           FROM orders o 
           JOIN clients c ON o.client_id = c.id 
           JOIN gas_types g ON o.gas_type_id = g.id 
           WHERE o.assigned_to = :staff_id 
           AND o.status IN ('assigned', 'refilling')
           ORDER BY o.priority DESC, o.created_at ASC");
$db->bind(':staff_id', $staff_id);
$assigned_orders = $db->resultset();

// Get today's completed orders
$db->query("SELECT COUNT(*) as completed_today FROM orders 
           WHERE assigned_to = :staff_id 
           AND status = 'filled' 
           AND DATE(refill_completed_at) = CURDATE()");
$db->bind(':staff_id', $staff_id);
$completed_today = $db->single()->completed_today;

// Get pending cylinders to fill
$db->query("SELECT oc.*, c.cylinder_code, c.barcode, c.size, c.weight_empty, c.weight_full,
           o.order_number, o.priority, cl.name as client_name, g.name as gas_name,
           l.name as location_name, t.current_level, t.capacity
           FROM order_cylinders oc
           JOIN cylinders c ON oc.cylinder_id = c.id
           JOIN orders o ON oc.order_id = o.id
           JOIN clients cl ON o.client_id = cl.id
           JOIN gas_types g ON o.gas_type_id = g.id
           LEFT JOIN locations l ON c.location_id = l.id
           LEFT JOIN tanks t ON t.gas_type_id = g.id AND t.is_active = 1
           WHERE o.assigned_to = :staff_id 
           AND oc.filled_at IS NULL
           AND o.status IN ('assigned', 'refilling')
           ORDER BY o.priority DESC, o.created_at ASC");
$db->bind(':staff_id', $staff_id);
$pending_cylinders = $db->resultset();

// Get staff statistics
$db->query("SELECT 
           COUNT(CASE WHEN o.status IN ('assigned', 'refilling') THEN 1 END) as active_orders,
           COUNT(CASE WHEN o.status = 'filled' AND DATE(o.refill_completed_at) = CURDATE() THEN 1 END) as today_completed,
           COUNT(CASE WHEN o.status = 'filled' AND WEEK(o.refill_completed_at) = WEEK(NOW()) THEN 1 END) as week_completed,
           COUNT(CASE WHEN oc.filled_at IS NOT NULL AND DATE(oc.filled_at) = CURDATE() THEN 1 END) as cylinders_filled_today
           FROM orders o
           LEFT JOIN order_cylinders oc ON o.id = oc.order_id
           WHERE o.assigned_to = :staff_id");
$db->bind(':staff_id', $staff_id);
$stats = $db->single();

$page_title = 'Refilling Dashboard';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-gas-pump"></i> Refilling Dashboard</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#scannerModal">
                            <i class="fas fa-qrcode"></i> Scan Cylinder
                        </button>
                        <a href="tasks.php" class="btn btn-sm btn-info">
                            <i class="fas fa-tasks"></i> My Tasks
                        </a>
                    </div>
                </div>
            </div>

            <!-- Flash Messages -->
            <?php displayFlashMessages(); ?>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Active Orders</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->active_orders; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Completed Today</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->today_completed; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Cylinders Filled Today</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->cylinders_filled_today; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-gas-pump fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Pending Cylinders</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo count($pending_cylinders); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Assigned Orders -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">My Assigned Orders (<?php echo count($assigned_orders); ?>)</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($assigned_orders)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No orders assigned</h5>
                                <p class="text-muted">You don't have any orders assigned for refilling at the moment.</p>
                            </div>
                            <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Order #</th>
                                            <th>Client</th>
                                            <th>Gas Type</th>
                                            <th>Priority</th>
                                            <th>Progress</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($assigned_orders as $order): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo $order->order_number; ?></strong>
                                                <br><small class="text-muted"><?php echo formatDateTime($order->created_at); ?></small>
                                            </td>
                                            <td>
                                                <?php echo $order->client_name; ?>
                                                <?php if ($order->client_phone): ?>
                                                <br><small class="text-muted"><?php echo $order->client_phone; ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $order->gas_name; ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $order->priority == 'high' ? 'danger' : ($order->priority == 'medium' ? 'warning' : 'secondary'); ?>">
                                                    <?php echo ucfirst($order->priority); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <?php $progress = $order->total_cylinders > 0 ? ($order->filled_cylinders / $order->total_cylinders) * 100 : 0; ?>
                                                    <div class="progress-bar bg-<?php echo $progress == 100 ? 'success' : 'info'; ?>" 
                                                         role="progressbar" style="width: <?php echo $progress; ?>%">
                                                        <?php echo $order->filled_cylinders; ?>/<?php echo $order->total_cylinders; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $order->status == 'assigned' ? 'warning' : 'info'; ?>">
                                                    <?php echo ucfirst($order->status); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="order_details.php?id=<?php echo $order->id; ?>" class="btn btn-sm btn-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($order->status == 'assigned'): ?>
                                                    <button type="button" class="btn btn-sm btn-success" 
                                                            onclick="startRefilling(<?php echo $order->id; ?>)" title="Start Refilling">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                    <?php if ($order->filled_cylinders == $order->total_cylinders && $order->status == 'refilling'): ?>
                                                    <button type="button" class="btn btn-sm btn-primary" 
                                                            onclick="completeRefilling(<?php echo $order->id; ?>)" title="Complete Order">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Cylinders to Fill -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Cylinders to Fill (<?php echo count($pending_cylinders); ?>)</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($pending_cylinders)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-gas-pump fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No cylinders to fill</h5>
                                <p class="text-muted">All assigned cylinders have been filled. Great job!</p>
                            </div>
                            <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Cylinder Code</th>
                                            <th>Order #</th>
                                            <th>Client</th>
                                            <th>Gas Type</th>
                                            <th>Size</th>
                                            <th>Weight</th>
                                            <th>Tank Level</th>
                                            <th>Priority</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($pending_cylinders as $cylinder): ?>
                                        <tr class="<?php echo $cylinder->priority == 'high' ? 'table-warning' : ''; ?>">
                                            <td>
                                                <strong><?php echo $cylinder->cylinder_code; ?></strong>
                                                <?php if ($cylinder->barcode): ?>
                                                <br><small class="text-muted"><?php echo $cylinder->barcode; ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $cylinder->order_number; ?></td>
                                            <td><?php echo $cylinder->client_name; ?></td>
                                            <td><?php echo $cylinder->gas_name; ?></td>
                                            <td><?php echo $cylinder->size; ?> kg</td>
                                            <td>
                                                Empty: <?php echo $cylinder->weight_empty; ?> kg
                                                <br>Full: <?php echo $cylinder->weight_full; ?> kg
                                            </td>
                                            <td>
                                                <?php if ($cylinder->current_level && $cylinder->capacity): ?>
                                                    <?php $tank_percentage = ($cylinder->current_level / $cylinder->capacity) * 100; ?>
                                                    <div class="progress" style="height: 15px;">
                                                        <div class="progress-bar bg-<?php echo $tank_percentage > 20 ? 'success' : ($tank_percentage > 10 ? 'warning' : 'danger'); ?>" 
                                                             style="width: <?php echo $tank_percentage; ?>%">
                                                            <?php echo round($tank_percentage, 1); ?>%
                                                        </div>
                                                    </div>
                                                    <small class="text-muted"><?php echo $cylinder->current_level; ?>/<?php echo $cylinder->capacity; ?> L</small>
                                                <?php else: ?>
                                                    <span class="text-muted">Tank info not available</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $cylinder->priority == 'high' ? 'danger' : ($cylinder->priority == 'medium' ? 'warning' : 'secondary'); ?>">
                                                    <?php echo ucfirst($cylinder->priority); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-success" 
                                                            onclick="markFilled(<?php echo $cylinder->id; ?>, '<?php echo $cylinder->cylinder_code; ?>')" 
                                                            title="Mark as Filled">
                                                        <i class="fas fa-check"></i> Fill
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-info" 
                                                            onclick="viewCylinder(<?php echo $cylinder->cylinder_id; ?>)" 
                                                            title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- QR/Barcode Scanner Modal -->
<div class="modal fade" id="scannerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Scan Cylinder QR Code or Barcode</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="scanner-container">
                    <div id="reader" style="width: 100%;"></div>
                    <div id="scanner-status" class="mt-3 text-center">
                        <button type="button" class="btn btn-primary" onclick="startScanner()">
                            <i class="fas fa-camera"></i> Start Scanner
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fill Confirmation Modal -->
<div class="modal fade" id="fillModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Cylinder Filling</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to mark cylinder <strong id="fillCylinderCode"></strong> as filled?</p>
                <div class="mb-3">
                    <label for="filledQuantity" class="form-label">Filled Quantity (kg)</label>
                    <input type="number" class="form-control" id="filledQuantity" step="0.01" placeholder="Enter filled quantity">
                </div>
                <div class="mb-3">
                    <label for="fillNotes" class="form-label">Notes (Optional)</label>
                    <textarea class="form-control" id="fillNotes" rows="2" placeholder="Any notes about the filling process..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="confirmFill()">
                    <i class="fas fa-check"></i> Mark as Filled
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentOrderCylinderId = null;
let html5QrcodeScanner = null;

function startRefilling(orderId) {
    if (confirm('Start refilling process for this order?')) {
        $.post('update_order_status.php', {
            order_id: orderId,
            status: 'refilling'
        }, function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error: ' + response.message);
            }
        }, 'json');
    }
}

function completeRefilling(orderId) {
    if (confirm('Mark this order as completely filled and ready for loading?')) {
        $.post('update_order_status.php', {
            order_id: orderId,
            status: 'filled'
        }, function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error: ' + response.message);
            }
        }, 'json');
    }
}

function markFilled(orderCylinderId, cylinderCode) {
    currentOrderCylinderId = orderCylinderId;
    document.getElementById('fillCylinderCode').textContent = cylinderCode;
    document.getElementById('filledQuantity').value = '';
    document.getElementById('fillNotes').value = '';
    $('#fillModal').modal('show');
}

function confirmFill() {
    const quantity = document.getElementById('filledQuantity').value;
    const notes = document.getElementById('fillNotes').value;
    
    if (!quantity || quantity <= 0) {
        alert('Please enter a valid filled quantity.');
        return;
    }
    
    $.post('mark_cylinder_filled.php', {
        order_cylinder_id: currentOrderCylinderId,
        filled_quantity: quantity,
        notes: notes
    }, function(response) {
        if (response.success) {
            $('#fillModal').modal('hide');
            location.reload();
        } else {
            alert('Error: ' + response.message);
        }
    }, 'json');
}

function viewCylinder(cylinderId) {
    window.open('../admin/cylinders/view.php?id=' + cylinderId, '_blank');
}

// Scanner functions
function startScanner() {
    if (html5QrcodeScanner) {
        html5QrcodeScanner.clear();
    }
    
    html5QrcodeScanner = new Html5QrcodeScanner("reader", {
        fps: 10,
        qrbox: { width: 250, height: 250 }
    }, false);
    
    html5QrcodeScanner.render(onScanSuccess, onScanFailure);
    document.getElementById('scanner-status').innerHTML = '<p class="text-success">Scanner active - Point camera at cylinder QR code or barcode</p>';
}

function onScanSuccess(decodedText, decodedResult) {
    // Search for cylinder in the pending list
    const rows = document.querySelectorAll('tbody tr');
    let found = false;
    
    rows.forEach(row => {
        const cylinderCode = row.cells[0].textContent.trim();
        if (cylinderCode.includes(decodedText)) {
            // Found the cylinder, trigger fill action
            const fillButton = row.querySelector('.btn-success');
            if (fillButton) {
                fillButton.click();
                found = true;
            }
        }
    });
    
    if (found) {
        document.getElementById('scanner-status').innerHTML = '<p class="text-success">Cylinder found! Fill dialog opened.</p>';
        $('#scannerModal').modal('hide');
    } else {
        document.getElementById('scanner-status').innerHTML = '<p class="text-warning">Cylinder not found in your pending list</p>';
    }
}

function onScanFailure(error) {
    // Handle scan failure silently
}
</script>

<?php include '../includes/footer.php'; ?>
