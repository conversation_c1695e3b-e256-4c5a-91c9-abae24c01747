<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and has refilling staff role
requireRole(['refilling_staff']);

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
    exit();
}

$order_cylinder_id = isset($_POST['order_cylinder_id']) ? (int)$_POST['order_cylinder_id'] : 0;
$filled_quantity = isset($_POST['filled_quantity']) ? (float)$_POST['filled_quantity'] : 0;
$notes = isset($_POST['notes']) ? sanitize($_POST['notes']) : '';

if (!$order_cylinder_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid order cylinder ID.']);
    exit();
}

if ($filled_quantity <= 0) {
    echo json_encode(['success' => false, 'message' => 'Please enter a valid filled quantity.']);
    exit();
}

try {
    // Get order cylinder details and verify staff assignment
    $db->query("SELECT oc.*, o.order_number, o.assigned_to, o.status as order_status,
               c.cylinder_code, c.size, c.weight_full, c.gas_type_id,
               cl.name as client_name, g.name as gas_name
               FROM order_cylinders oc
               JOIN orders o ON oc.order_id = o.id
               JOIN cylinders c ON oc.cylinder_id = c.id
               JOIN clients cl ON o.client_id = cl.id
               JOIN gas_types g ON o.gas_type_id = g.id
               WHERE oc.id = :id");
    $db->bind(':id', $order_cylinder_id);
    $order_cylinder = $db->single();
    
    if (!$order_cylinder) {
        echo json_encode(['success' => false, 'message' => 'Order cylinder not found.']);
        exit();
    }
    
    // Verify this staff member is assigned to this order
    if ($order_cylinder->assigned_to != $_SESSION['user_id']) {
        echo json_encode(['success' => false, 'message' => 'You are not assigned to this order.']);
        exit();
    }
    
    // Check if cylinder is already filled
    if ($order_cylinder->filled_at) {
        echo json_encode(['success' => false, 'message' => 'This cylinder has already been filled.']);
        exit();
    }
    
    // Check if order is in correct status
    if (!in_array($order_cylinder->order_status, ['assigned', 'refilling'])) {
        echo json_encode(['success' => false, 'message' => 'Order is not in a status that allows filling.']);
        exit();
    }
    
    // Validate filled quantity (should not exceed cylinder capacity)
    if ($filled_quantity > $order_cylinder->weight_full) {
        echo json_encode(['success' => false, 'message' => "Filled quantity ({$filled_quantity} kg) exceeds cylinder capacity ({$order_cylinder->weight_full} kg).']);
        exit();
    }
    
    // Check tank availability for this gas type
    $db->query("SELECT * FROM tanks WHERE gas_type_id = :gas_type_id AND is_active = 1 ORDER BY current_level DESC LIMIT 1");
    $db->bind(':gas_type_id', $order_cylinder->gas_type_id);
    $tank = $db->single();
    
    if (!$tank) {
        echo json_encode(['success' => false, 'message' => 'No active tank found for this gas type.']);
        exit();
    }
    
    // Check if tank has enough gas
    if ($tank->current_level < $filled_quantity) {
        echo json_encode(['success' => false, 'message' => "Insufficient gas in tank. Available: {$tank->current_level} L, Required: {$filled_quantity} L"]);
        exit();
    }
    
    $db->beginTransaction();
    
    // Mark cylinder as filled
    $db->query("UPDATE order_cylinders SET 
               filled_quantity = :filled_quantity,
               filled_at = NOW(),
               filled_by = :filled_by
               WHERE id = :id");
    $db->bind(':filled_quantity', $filled_quantity);
    $db->bind(':filled_by', $_SESSION['user_id']);
    $db->bind(':id', $order_cylinder_id);
    $db->execute();
    
    // Update cylinder status
    $db->query("UPDATE cylinders SET status = 'filled', updated_at = NOW() WHERE id = :id");
    $db->bind(':id', $order_cylinder->cylinder_id);
    $db->execute();
    
    // Update tank level
    $db->query("UPDATE tanks SET 
               current_level = current_level - :quantity,
               last_refill_date = NOW(),
               updated_at = NOW()
               WHERE id = :id");
    $db->bind(':quantity', $filled_quantity);
    $db->bind(':id', $tank->id);
    $db->execute();
    
    // Log tank usage
    $db->query("INSERT INTO tank_usage_logs 
               (tank_id, usage_type, quantity, cylinder_id, order_id, notes, logged_by, logged_at)
               VALUES (:tank_id, 'refill', :quantity, :cylinder_id, :order_id, :notes, :logged_by, NOW())");
    $db->bind(':tank_id', $tank->id);
    $db->bind(':quantity', $filled_quantity);
    $db->bind(':cylinder_id', $order_cylinder->cylinder_id);
    $db->bind(':order_id', $order_cylinder->order_id);
    $db->bind(':notes', $notes ? "Cylinder filling: " . $notes : "Cylinder {$order_cylinder->cylinder_code} filled");
    $db->bind(':logged_by', $_SESSION['user_id']);
    $db->execute();
    
    // Check if this was the last cylinder for the order
    $db->query("SELECT COUNT(*) as total_cylinders,
               COUNT(CASE WHEN filled_at IS NOT NULL THEN 1 END) as filled_cylinders
               FROM order_cylinders WHERE order_id = :order_id");
    $db->bind(':order_id', $order_cylinder->order_id);
    $order_progress = $db->single();
    
    // Update order status if needed
    if ($order_cylinder->order_status == 'assigned') {
        // First cylinder filled, change status to refilling
        $db->query("UPDATE orders SET status = 'refilling', updated_at = NOW() WHERE id = :id");
        $db->bind(':id', $order_cylinder->order_id);
        $db->execute();
    } elseif ($order_progress->filled_cylinders == $order_progress->total_cylinders) {
        // All cylinders filled, change status to filled
        $db->query("UPDATE orders SET 
                   status = 'filled',
                   refill_completed_at = NOW(),
                   updated_at = NOW()
                   WHERE id = :id");
        $db->bind(':id', $order_cylinder->order_id);
        $db->execute();
    }
    
    $db->commit();
    
    // Log activity
    logActivity($_SESSION['user_id'], 'cylinder_filled', 
               "Filled cylinder {$order_cylinder->cylinder_code} for order {$order_cylinder->order_number} with {$filled_quantity} kg of {$order_cylinder->gas_name}");
    
    $response_message = "Cylinder {$order_cylinder->cylinder_code} marked as filled successfully!";
    
    // Add order completion message if applicable
    if ($order_progress->filled_cylinders == $order_progress->total_cylinders) {
        $response_message .= " Order {$order_cylinder->order_number} is now complete and ready for loading.";
    }
    
    echo json_encode([
        'success' => true, 
        'message' => $response_message,
        'order_complete' => $order_progress->filled_cylinders == $order_progress->total_cylinders
    ]);
    
} catch (Exception $e) {
    $db->rollback();
    echo json_encode(['success' => false, 'message' => 'Error marking cylinder as filled: ' . $e->getMessage()]);
}
?>
