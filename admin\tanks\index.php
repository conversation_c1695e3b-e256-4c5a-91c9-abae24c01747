<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

// Handle search and filters
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$gas_type_filter = isset($_GET['gas_type']) ? (int)$_GET['gas_type'] : 0;
$location_filter = isset($_GET['location']) ? (int)$_GET['location'] : 0;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$records_per_page = 25;

// Build query
$where_conditions = ['t.is_active = 1'];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "t.tank_code LIKE :search";
    $params[':search'] = "%$search%";
}

if (!empty($gas_type_filter)) {
    $where_conditions[] = "t.gas_type_id = :gas_type";
    $params[':gas_type'] = $gas_type_filter;
}

if (!empty($location_filter)) {
    $where_conditions[] = "t.location_id = :location";
    $params[':location'] = $location_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count for pagination
$count_query = "SELECT COUNT(*) as total FROM tanks t WHERE $where_clause";
$db->query($count_query);
foreach ($params as $key => $value) {
    $db->bind($key, $value);
}
$total_records = $db->single()->total;

// Calculate pagination
$pagination = paginate($total_records, $records_per_page, $page);

// Get tanks
$query = "SELECT t.*, g.name as gas_name, g.code as gas_code, l.name as location_name,
          ROUND((t.current_level / t.capacity) * 100, 2) as level_percentage
          FROM tanks t 
          JOIN gas_types g ON t.gas_type_id = g.id 
          JOIN locations l ON t.location_id = l.id 
          WHERE $where_clause 
          ORDER BY level_percentage ASC, t.created_at DESC 
          LIMIT {$pagination['limit']} OFFSET {$pagination['offset']}";

$db->query($query);
foreach ($params as $key => $value) {
    $db->bind($key, $value);
}
$tanks = $db->resultset();

// Get filter options
$db->query("SELECT * FROM gas_types WHERE is_active = 1 ORDER BY name");
$gas_types = $db->resultset();

$db->query("SELECT * FROM locations WHERE is_active = 1 ORDER BY name");
$locations = $db->resultset();

// Get statistics
$db->query("SELECT 
    COUNT(*) as total_tanks,
    SUM(capacity) as total_capacity,
    SUM(current_level) as total_current,
    AVG((current_level / capacity) * 100) as avg_level,
    SUM(CASE WHEN (current_level / capacity) * 100 < 20 THEN 1 ELSE 0 END) as low_level_tanks
    FROM tanks WHERE is_active = 1");
$stats = $db->single();

$page_title = 'Tank Management';
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-gas-pump"></i> Tank Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="create.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> Add New Tank
                        </a>
                        <a href="refill.php" class="btn btn-sm btn-success">
                            <i class="fas fa-fill-drip"></i> Refill Tank
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToCSV('tanksTable', 'tanks')">
                            <i class="fas fa-download"></i> Export CSV
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Tanks</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->total_tanks; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-gas-pump fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Capacity</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats->total_capacity, 1); ?> kg</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-weight fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Current Stock</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats->total_current, 1); ?> kg</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Avg Level</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats->avg_level, 1); ?>%</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-percentage fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-left-danger shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Low Level</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->low_level_tanks; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Tank code">
                        </div>
                        <div class="col-md-3">
                            <label for="gas_type" class="form-label">Gas Type</label>
                            <select class="form-select" id="gas_type" name="gas_type">
                                <option value="">All Gas Types</option>
                                <?php foreach ($gas_types as $gas_type): ?>
                                    <option value="<?php echo $gas_type->id; ?>" <?php echo $gas_type_filter == $gas_type->id ? 'selected' : ''; ?>>
                                        <?php echo $gas_type->name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="location" class="form-label">Location</label>
                            <select class="form-select" id="location" name="location">
                                <option value="">All Locations</option>
                                <?php foreach ($locations as $location): ?>
                                    <option value="<?php echo $location->id; ?>" <?php echo $location_filter == $location->id ? 'selected' : ''; ?>>
                                        <?php echo $location->name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search"></i> Search
                            </button>
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tanks Grid -->
            <div class="row">
                <?php foreach ($tanks as $tank): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card shadow h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary"><?php echo $tank->tank_code; ?></h6>
                            <span class="badge bg-secondary"><?php echo $tank->gas_code; ?></span>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <!-- Animated Tank -->
                                <div class="tank-animation mx-auto" id="tank_<?php echo $tank->id; ?>" 
                                     data-percentage="<?php echo $tank->level_percentage; ?>" 
                                     style="width: 80px; height: 120px; position: relative; border: 3px solid #333; border-radius: 0 0 40px 40px; background: #f0f0f0; overflow: hidden;">
                                    <div class="tank-fill" style="position: absolute; bottom: 0; width: 100%; height: <?php echo $tank->level_percentage; ?>%; 
                                         background: linear-gradient(135deg, 
                                         <?php echo $tank->level_percentage < 20 ? '#dc3545, #ff6b6b' : 
                                                    ($tank->level_percentage < 50 ? '#ffc107, #ffeb3b' : '#28a745, #4caf50'); ?>); 
                                         transition: height 0.5s ease;">
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <h5 class="<?php echo $tank->level_percentage < 20 ? 'text-danger' : ($tank->level_percentage < 50 ? 'text-warning' : 'text-success'); ?>">
                                        <?php echo $tank->level_percentage; ?>%
                                    </h5>
                                </div>
                            </div>

                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td><strong>Gas Type:</strong></td>
                                    <td><?php echo $tank->gas_name; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Location:</strong></td>
                                    <td><?php echo $tank->location_name; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Capacity:</strong></td>
                                    <td><?php echo $tank->capacity; ?> <?php echo $tank->unit; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Current:</strong></td>
                                    <td><?php echo $tank->current_level; ?> <?php echo $tank->unit; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Last Filled:</strong></td>
                                    <td><?php echo $tank->last_filled_date ? formatDate($tank->last_filled_date) : '-'; ?></td>
                                </tr>
                            </table>

                            <?php if ($tank->level_percentage < $tank->wastage_threshold): ?>
                            <div class="alert alert-warning alert-sm">
                                <i class="fas fa-exclamation-triangle"></i> Low level alert!
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100" role="group">
                                <a href="view.php?id=<?php echo $tank->id; ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="refill.php?id=<?php echo $tank->id; ?>" class="btn btn-sm btn-success">
                                    <i class="fas fa-fill-drip"></i> Refill
                                </a>
                                <a href="edit.php?id=<?php echo $tank->id; ?>" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($pagination['total_pages'] > 1): ?>
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    <?php if ($pagination['has_previous']): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $pagination['current_page'] - 1; ?>&search=<?php echo urlencode($search); ?>&gas_type=<?php echo $gas_type_filter; ?>&location=<?php echo $location_filter; ?>">Previous</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = 1; $i <= $pagination['total_pages']; $i++): ?>
                        <li class="page-item <?php echo $i == $pagination['current_page'] ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&gas_type=<?php echo $gas_type_filter; ?>&location=<?php echo $location_filter; ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($pagination['has_next']): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $pagination['current_page'] + 1; ?>&search=<?php echo urlencode($search); ?>&gas_type=<?php echo $gas_type_filter; ?>&location=<?php echo $location_filter; ?>">Next</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </main>
    </div>
</div>

<script>
// Animate tank levels on page load
document.addEventListener('DOMContentLoaded', function() {
    const tanks = document.querySelectorAll('.tank-animation');
    tanks.forEach(function(tank, index) {
        setTimeout(function() {
            const percentage = tank.dataset.percentage;
            const fill = tank.querySelector('.tank-fill');
            if (fill) {
                fill.style.height = percentage + '%';
            }
        }, index * 200); // Stagger animations
    });
});
</script>

<?php include '../../includes/footer.php'; ?>
